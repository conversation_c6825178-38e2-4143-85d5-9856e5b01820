<?php
/**
 * District Manager Class
 * 
 * Handles all CRUD operations for districts
 * 
 * @package Dakoii_Province_District_Manager
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Dakoii_PDM_District_Manager
{
    /**
     * WordPress database object
     *
     * @var wpdb
     */
    private $wpdb;

    /**
     * Districts table name
     *
     * @var string
     */
    private $table_name;

    /**
     * Provinces table name
     *
     * @var string
     */
    private $provinces_table;

    /**
     * Constructor
     */
    public function __construct()
    {
        global $wpdb;
        $this->wpdb = $wpdb;
        $this->table_name = $wpdb->prefix . 'dakoii_districts';
        $this->provinces_table = $wpdb->prefix . 'dakoii_provinces';
    }

    /**
     * Create a new district
     *
     * @param array $data District data
     * @return int|WP_Error District ID on success, WP_Error on failure
     */
    public function create($data)
    {
        // Validate required fields
        $validation = $this->validate_data($data);
        if (is_wp_error($validation)) {
            return $validation;
        }

        // Prepare data for insertion
        $district_data = $this->prepare_data($data);
        
        // Check if slug already exists
        if ($this->slug_exists($district_data['slug'])) {
            return new WP_Error('duplicate_slug', __('District slug already exists.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Check if province exists
        if (!$this->province_exists($district_data['province_id'])) {
            return new WP_Error('invalid_province', __('Selected province does not exist.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Insert into database
        $result = $this->wpdb->insert(
            $this->table_name,
            $district_data,
            array('%d', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s', '%s')
        );

        if ($result === false) {
            return new WP_Error('db_error', __('Failed to create district.', DAKOII_PDM_TEXT_DOMAIN));
        }

        $district_id = $this->wpdb->insert_id;

        // Fire action hook
        do_action('dakoii_pdm_district_created', $district_id, $district_data);

        return $district_id;
    }

    /**
     * Read a district by ID
     *
     * @param int $id District ID
     * @return object|null District object or null if not found
     */
    public function read($id)
    {
        $district = $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT d.*, p.name as province_name 
                 FROM {$this->table_name} d 
                 LEFT JOIN {$this->provinces_table} p ON d.province_id = p.id 
                 WHERE d.id = %d",
                $id
            )
        );

        if ($district) {
            $district = $this->format_district_data($district);
        }

        return $district;
    }

    /**
     * Read a district by slug
     *
     * @param string $slug District slug
     * @return object|null District object or null if not found
     */
    public function read_by_slug($slug)
    {
        $district = $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT d.*, p.name as province_name 
                 FROM {$this->table_name} d 
                 LEFT JOIN {$this->provinces_table} p ON d.province_id = p.id 
                 WHERE d.slug = %s",
                $slug
            )
        );

        if ($district) {
            $district = $this->format_district_data($district);
        }

        return $district;
    }

    /**
     * Update a district
     *
     * @param int $id District ID
     * @param array $data Updated district data
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public function update($id, $data)
    {
        // Check if district exists
        $existing_district = $this->read($id);
        if (!$existing_district) {
            return new WP_Error('not_found', __('District not found.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Validate data
        $validation = $this->validate_data($data, $id);
        if (is_wp_error($validation)) {
            return $validation;
        }

        // Prepare data for update
        $district_data = $this->prepare_data($data, true);

        // Check if slug already exists (excluding current district)
        if (isset($district_data['slug']) && $this->slug_exists($district_data['slug'], $id)) {
            return new WP_Error('duplicate_slug', __('District slug already exists.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Check if province exists
        if (isset($district_data['province_id']) && !$this->province_exists($district_data['province_id'])) {
            return new WP_Error('invalid_province', __('Selected province does not exist.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Update database
        $format = array();
        foreach (array_keys($district_data) as $key) {
            if (in_array($key, array('province_id'))) {
                $format[] = '%d';
            } else {
                $format[] = '%s';
            }
        }

        $result = $this->wpdb->update(
            $this->table_name,
            $district_data,
            array('id' => $id),
            $format,
            array('%d')
        );

        if ($result === false) {
            return new WP_Error('db_error', __('Failed to update district.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Fire action hook
        do_action('dakoii_pdm_district_updated', $id, $district_data, $existing_district);

        return true;
    }

    /**
     * Delete a district
     *
     * @param int $id District ID
     * @param bool $force_delete Whether to force delete even with content
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public function delete($id, $force_delete = false)
    {
        // Check if district exists
        $district = $this->read($id);
        if (!$district) {
            return new WP_Error('not_found', __('District not found.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Check for associated content
        if (!$force_delete) {
            $content_count = $this->get_content_count($id);
            if ($content_count > 0) {
                return new WP_Error(
                    'has_content', 
                    sprintf(
                        __('Cannot delete district. It has %d associated content item(s).', DAKOII_PDM_TEXT_DOMAIN),
                        $content_count
                    )
                );
            }
        }

        // Delete from database
        $result = $this->wpdb->delete(
            $this->table_name,
            array('id' => $id),
            array('%d')
        );

        if ($result === false) {
            return new WP_Error('db_error', __('Failed to delete district.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Fire action hook
        do_action('dakoii_pdm_district_deleted', $id, $district);

        return true;
    }

    /**
     * Get all districts with pagination
     *
     * @param array $args Query arguments
     * @return array Array with districts and pagination info
     */
    public function get_all($args = array())
    {
        $defaults = array(
            'province_id' => 0,
            'status' => 'active',
            'search' => '',
            'orderby' => 'name',
            'order' => 'ASC',
            'limit' => 20,
            'offset' => 0,
            'include_counts' => true
        );

        $args = wp_parse_args($args, $defaults);

        // Build WHERE clause
        $where_conditions = array();
        $where_values = array();

        if (!empty($args['province_id'])) {
            $where_conditions[] = "d.province_id = %d";
            $where_values[] = $args['province_id'];
        }

        if (!empty($args['status'])) {
            $where_conditions[] = "d.status = %s";
            $where_values[] = $args['status'];
        }

        if (!empty($args['search'])) {
            $where_conditions[] = "(d.name LIKE %s OR d.description LIKE %s)";
            $search_term = '%' . $this->wpdb->esc_like($args['search']) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }

        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

        // Build ORDER BY clause
        $allowed_orderby = array('id', 'name', 'slug', 'status', 'created_at', 'updated_at', 'province_name');
        $orderby = in_array($args['orderby'], $allowed_orderby) ? $args['orderby'] : 'name';
        
        if ($orderby === 'province_name') {
            $orderby = 'p.name';
        } else {
            $orderby = 'd.' . $orderby;
        }
        
        $order = strtoupper($args['order']) === 'DESC' ? 'DESC' : 'ASC';

        // Get total count
        $count_sql = "SELECT COUNT(*) FROM {$this->table_name} d 
                      LEFT JOIN {$this->provinces_table} p ON d.province_id = p.id 
                      {$where_clause}";
        if (!empty($where_values)) {
            $count_sql = $this->wpdb->prepare($count_sql, $where_values);
        }
        $total_count = $this->wpdb->get_var($count_sql);

        // Get districts
        $sql = "SELECT d.*, p.name as province_name 
                FROM {$this->table_name} d 
                LEFT JOIN {$this->provinces_table} p ON d.province_id = p.id 
                {$where_clause} 
                ORDER BY {$orderby} {$order}";
        
        if ($args['limit'] > 0) {
            $sql .= $this->wpdb->prepare(" LIMIT %d OFFSET %d", $args['limit'], $args['offset']);
        }

        if (!empty($where_values)) {
            $sql = $this->wpdb->prepare($sql, $where_values);
        }

        $districts = $this->wpdb->get_results($sql);

        // Format district data
        foreach ($districts as &$district) {
            $district = $this->format_district_data($district);
            
            if ($args['include_counts']) {
                $district->content_count = $this->get_content_count($district->id);
            }
        }

        return array(
            'districts' => $districts,
            'total_count' => $total_count,
            'found_rows' => count($districts)
        );
    }

    /**
     * Get districts by province ID
     *
     * @param int $province_id Province ID
     * @param string $status District status
     * @return array Array of districts
     */
    public function get_by_province($province_id, $status = 'active')
    {
        $districts = $this->wpdb->get_results(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->table_name} WHERE province_id = %d AND status = %s ORDER BY name ASC",
                $province_id,
                $status
            )
        );

        foreach ($districts as &$district) {
            $district = $this->format_district_data($district);
        }

        return $districts;
    }

    /**
     * Get district options for select fields
     *
     * @param int $province_id Province ID (optional)
     * @param string $status District status
     * @return array Array of district options
     */
    public function get_options($province_id = 0, $status = 'active')
    {
        $sql = "SELECT id, name FROM {$this->table_name} WHERE status = %s";
        $values = array($status);

        if ($province_id > 0) {
            $sql .= " AND province_id = %d";
            $values[] = $province_id;
        }

        $sql .= " ORDER BY name ASC";

        $districts = $this->wpdb->get_results(
            $this->wpdb->prepare($sql, $values)
        );

        $options = array();
        foreach ($districts as $district) {
            $options[$district->id] = $district->name;
        }

        return $options;
    }

    /**
     * Check if slug exists
     *
     * @param string $slug District slug
     * @param int $exclude_id ID to exclude from check
     * @return bool True if slug exists, false otherwise
     */
    private function slug_exists($slug, $exclude_id = 0)
    {
        $sql = "SELECT COUNT(*) FROM {$this->table_name} WHERE slug = %s";
        $values = array($slug);

        if ($exclude_id > 0) {
            $sql .= " AND id != %d";
            $values[] = $exclude_id;
        }

        $count = $this->wpdb->get_var($this->wpdb->prepare($sql, $values));
        
        return $count > 0;
    }

    /**
     * Check if province exists
     *
     * @param int $province_id Province ID
     * @return bool True if province exists, false otherwise
     */
    private function province_exists($province_id)
    {
        $count = $this->wpdb->get_var(
            $this->wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->provinces_table} WHERE id = %d AND status = 'active'",
                $province_id
            )
        );
        
        return $count > 0;
    }

    /**
     * Get content count for a district
     *
     * @param int $district_id District ID
     * @return int Content count
     */
    private function get_content_count($district_id)
    {
        $content_table = $this->wpdb->prefix . 'dakoii_district_content';
        
        return $this->wpdb->get_var(
            $this->wpdb->prepare(
                "SELECT COUNT(*) FROM {$content_table} WHERE district_id = %d",
                $district_id
            )
        );
    }

    /**
     * Validate district data
     *
     * @param array $data District data
     * @param int $id District ID (for updates)
     * @return bool|WP_Error True if valid, WP_Error on validation failure
     */
    private function validate_data($data, $id = 0)
    {
        $errors = new WP_Error();

        // Validate required fields
        if (empty($data['name'])) {
            $errors->add('missing_name', __('District name is required.', DAKOII_PDM_TEXT_DOMAIN));
        }

        if (empty($data['province_id']) && $id === 0) {
            $errors->add('missing_province', __('Province is required.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Validate field lengths
        if (isset($data['name']) && strlen($data['name']) > 255) {
            $errors->add('name_too_long', __('District name must be 255 characters or less.', DAKOII_PDM_TEXT_DOMAIN));
        }

        if (isset($data['slug']) && strlen($data['slug']) > 255) {
            $errors->add('slug_too_long', __('District slug must be 255 characters or less.', DAKOII_PDM_TEXT_DOMAIN));
        }

        if (isset($data['logo_url']) && strlen($data['logo_url']) > 500) {
            $errors->add('logo_url_too_long', __('Logo URL must be 500 characters or less.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Validate slug format
        if (isset($data['slug']) && !preg_match('/^[a-z0-9-]+$/', $data['slug'])) {
            $errors->add('invalid_slug', __('District slug can only contain lowercase letters, numbers, and hyphens.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Validate status
        if (isset($data['status']) && !in_array($data['status'], array('active', 'inactive'))) {
            $errors->add('invalid_status', __('Invalid district status.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Validate province ID
        if (isset($data['province_id']) && !is_numeric($data['province_id'])) {
            $errors->add('invalid_province_id', __('Invalid province ID.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Validate JSON fields
        if (isset($data['contact_info']) && !empty($data['contact_info']) && !is_array($data['contact_info'])) {
            if (json_decode($data['contact_info']) === null) {
                $errors->add('invalid_contact_info', __('Invalid contact information format.', DAKOII_PDM_TEXT_DOMAIN));
            }
        }

        return $errors->get_error_codes() ? $errors : true;
    }

    /**
     * Prepare district data for database insertion/update
     *
     * @param array $data Raw district data
     * @param bool $is_update Whether this is an update operation
     * @return array Prepared data
     */
    private function prepare_data($data, $is_update = false)
    {
        $prepared = array();

        // Required fields
        if (isset($data['province_id'])) {
            $prepared['province_id'] = (int) $data['province_id'];
        }

        if (isset($data['name'])) {
            $prepared['name'] = sanitize_text_field($data['name']);
        }

        // Generate slug if not provided
        if (isset($data['slug'])) {
            $prepared['slug'] = sanitize_title($data['slug']);
        } elseif (isset($data['name']) && !$is_update) {
            $prepared['slug'] = sanitize_title($data['name']);
        }

        // Optional fields
        if (isset($data['description'])) {
            $prepared['description'] = sanitize_textarea_field($data['description']);
        }

        if (isset($data['logo_url'])) {
            $prepared['logo_url'] = esc_url_raw($data['logo_url']);
        }

        if (isset($data['contact_info'])) {
            if (is_array($data['contact_info'])) {
                $prepared['contact_info'] = json_encode($data['contact_info']);
            } else {
                $prepared['contact_info'] = $data['contact_info'];
            }
        }

        if (isset($data['menu_settings'])) {
            if (is_array($data['menu_settings'])) {
                $prepared['menu_settings'] = json_encode($data['menu_settings']);
            } else {
                $prepared['menu_settings'] = $data['menu_settings'];
            }
        }

        if (isset($data['theme_settings'])) {
            if (is_array($data['theme_settings'])) {
                $prepared['theme_settings'] = json_encode($data['theme_settings']);
            } else {
                $prepared['theme_settings'] = $data['theme_settings'];
            }
        }

        if (isset($data['status'])) {
            $prepared['status'] = sanitize_text_field($data['status']);
        } elseif (!$is_update) {
            $prepared['status'] = 'active';
        }

        return $prepared;
    }

    /**
     * Format district data for output
     *
     * @param object $district Raw district data from database
     * @return object Formatted district data
     */
    private function format_district_data($district)
    {
        // Ensure all expected properties exist
        $district->id = (int) $district->id;
        $district->province_id = (int) $district->province_id;
        $district->name = $district->name ?: '';
        $district->slug = $district->slug ?: '';
        $district->description = $district->description ?: '';
        $district->logo_url = $district->logo_url ?: '';
        $district->status = $district->status ?: 'active';
        $district->province_name = $district->province_name ?? '';

        // Decode JSON fields
        $district->contact_info = !empty($district->contact_info) ? json_decode($district->contact_info, true) : array();
        $district->menu_settings = !empty($district->menu_settings) ? json_decode($district->menu_settings, true) : array();
        $district->theme_settings = !empty($district->theme_settings) ? json_decode($district->theme_settings, true) : array();
        
        // Format dates
        if (!empty($district->created_at)) {
            $district->created_at_formatted = date_i18n(get_option('date_format'), strtotime($district->created_at));
        }
        
        if (!empty($district->updated_at)) {
            $district->updated_at_formatted = date_i18n(get_option('date_format'), strtotime($district->updated_at));
        }

        return $district;
    }

    /**
     * Get district statistics
     *
     * @return array Statistics array
     */
    public function get_statistics()
    {
        $stats = array();

        // Total districts
        $stats['total'] = $this->wpdb->get_var("SELECT COUNT(*) FROM {$this->table_name}");
        
        // Active districts
        $stats['active'] = $this->wpdb->get_var(
            $this->wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->table_name} WHERE status = %s",
                'active'
            )
        );
        
        // Inactive districts
        $stats['inactive'] = $stats['total'] - $stats['active'];

        // Districts by province
        $stats['by_province'] = $this->wpdb->get_results(
            "SELECT p.name as province_name, COUNT(d.id) as district_count 
             FROM {$this->provinces_table} p 
             LEFT JOIN {$this->table_name} d ON p.id = d.province_id 
             GROUP BY p.id, p.name 
             ORDER BY p.name",
            ARRAY_A
        );

        return $stats;
    }
}