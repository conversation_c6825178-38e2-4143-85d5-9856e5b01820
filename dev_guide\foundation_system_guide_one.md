# Dakoii Province and District Manager - Foundation System Guide

**Author:** <PERSON><PERSON>  
**Website:** www.dakoiims.com  
**Email:** <EMAIL>  
**Version:** 1.0.0  
**Last Updated:** September 7, 2025  

## Plugin Overview

The **Dakoii Province and District Manager** is a WordPress plugin that enables hierarchical management of provinces and districts, where each district functions as a mini-website with its own content management capabilities.

## Core Features

### 1. Administrative CRUD Operations
- Create, Read, Update, Delete provinces
- Create, Read, Update, Delete districts within provinces
- Hierarchical relationship management
- Bulk operations for efficient management

### 2. District Mini-Website Functionality
- Each district behaves like an independent website
- Custom menu structure for each district
- Isolated content management per district
- District-specific branding and customization

### 3. Content Management System
- Districts can create posts using WordPress post types
- Districts can create pages
- Districts can manage their own submenu structure
- All district content appears in district-specific submenus

## Technical Architecture

### 1. Database Schema

#### Provinces Table (`wp_dakoii_provinces`)
```sql
CREATE TABLE wp_dakoii_provinces (
    id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    INDEX idx_slug (slug),
    INDEX idx_status (status)
);
```

#### Districts Table (`wp_dakoii_districts`)
```sql
CREATE TABLE wp_dakoii_districts (
    id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    province_id BIGINT(20) UNSIGNED NOT NULL,
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(255) NOT NULL UNIQUE,
    description TEXT,
    logo_url VARCHAR(500),
    contact_info JSON,
    menu_settings JSON,
    theme_settings JSON,
    status ENUM('active', 'inactive') DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (province_id) REFERENCES wp_dakoii_provinces(id) ON DELETE CASCADE,
    INDEX idx_province_id (province_id),
    INDEX idx_slug (slug),
    INDEX idx_status (status)
);
```

#### District Content Association (`wp_dakoii_district_content`)
```sql
CREATE TABLE wp_dakoii_district_content (
    id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
    district_id BIGINT(20) UNSIGNED NOT NULL,
    post_id BIGINT(20) UNSIGNED NOT NULL,
    content_type ENUM('post', 'page', 'menu') NOT NULL,
    menu_order INT(11) DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (id),
    FOREIGN KEY (district_id) REFERENCES wp_dakoii_districts(id) ON DELETE CASCADE,
    FOREIGN KEY (post_id) REFERENCES wp_posts(ID) ON DELETE CASCADE,
    UNIQUE KEY unique_district_post (district_id, post_id),
    INDEX idx_district_id (district_id),
    INDEX idx_post_id (post_id),
    INDEX idx_content_type (content_type)
);
```

### 2. Plugin File Structure

```
dakoii-prov-district-manager/
├── dakoii-prov-district-manager.php (Main plugin file)
├── includes/
│   ├── class-dakoii-plugin.php (Main plugin class)
│   ├── class-database-manager.php (Database operations)
│   ├── class-province-manager.php (Province CRUD)
│   ├── class-district-manager.php (District CRUD)
│   ├── class-content-manager.php (Content association)
│   ├── class-menu-manager.php (Menu management)
│   ├── class-admin-interface.php (Admin UI)
│   └── class-frontend-handler.php (Frontend display)
├── admin/
│   ├── css/
│   │   └── admin-styles.css
│   ├── js/
│   │   └── admin-scripts.js
│   └── views/
│       ├── provinces-list.php
│       ├── provinces-form.php
│       ├── districts-list.php
│       ├── districts-form.php
│       └── dashboard.php
├── frontend/
│   ├── css/
│   │   └── frontend-styles.css
│   ├── js/
│   │   └── frontend-scripts.js
│   └── templates/
│       ├── district-home.php
│       ├── district-posts.php
│       └── district-pages.php
├── assets/
│   └── images/
└── languages/
    └── dakoii-prov-district-manager.pot
```

## Implementation Strategy

### Phase 1: Core Foundation (Week 1-2)

#### Step 1.1: Plugin Bootstrap
1. Create main plugin file with proper headers
2. Set up autoloading and dependency injection
3. Initialize database tables on activation
4. Create uninstall cleanup procedures

#### Step 1.2: Database Layer
1. Implement `DatabaseManager` class for table creation/management
2. Create migration system for future updates
3. Set up proper foreign key relationships
4. Implement database cleanup on plugin removal

#### Step 1.3: Core Models
1. Create `Province` model with CRUD operations
2. Create `District` model with CRUD operations
3. Implement validation and sanitization
4. Set up model relationships and constraints

### Phase 2: Administrative Interface (Week 3-4)

#### Step 2.1: Admin Menu Structure
```php
// Admin menu hierarchy
Dakoii Manager
├── Dashboard
├── Provinces
│   ├── All Provinces
│   └── Add New Province
├── Districts
│   ├── All Districts
│   └── Add New District
└── Settings
```

#### Step 2.2: CRUD Interfaces
1. Province management interface
   - List view with search, filter, bulk actions
   - Add/Edit form with validation
   - Delete confirmation with cascade options

2. District management interface
   - List view grouped by province
   - Add/Edit form with province selection
   - Logo upload and contact information
   - Menu and theme customization options

#### Step 2.3: Admin Dashboard
1. Statistics overview (provinces, districts, content)
2. Recent activity feed
3. Quick action buttons
4. System health indicators

### Phase 3: Content Management System (Week 5-6)

#### Step 3.1: Content Association System
1. Create meta boxes for post/page assignment to districts
2. Implement content filtering by district
3. Set up automatic content categorization
4. Create content migration tools

#### Step 3.2: District Content Interface
1. Custom post list tables filtered by district
2. District-specific content creation workflows
3. Content approval system (if needed)
4. Bulk content operations

### Phase 4: Frontend Menu System (Week 7-8)

#### Step 4.1: Dynamic Menu Generation
1. Create custom menu walker for district menus
2. Implement automatic submenu creation
3. Set up menu caching for performance
4. Create menu customization interface

#### Step 4.2: Menu Integration Points
```php
// Menu structure example
Main Menu Item
├── District A
│   ├── District A Home
│   ├── District A Posts
│   │   ├── Post 1
│   │   └── Post 2
│   └── District A Pages
│       ├── About District A
│       └── Services
└── District B
    ├── District B Home
    └── District B Content...
```

#### Step 4.3: URL Structure Design
```
/district/{district-slug}/
/district/{district-slug}/posts/
/district/{district-slug}/pages/
/district/{district-slug}/post/{post-slug}/
/district/{district-slug}/page/{page-slug}/
```

### Phase 5: Frontend Display System (Week 9-10)

#### Step 5.1: Custom Post Templates
1. Create district-specific post templates
2. Implement template hierarchy
3. Set up district branding integration
4. Create responsive layouts

#### Step 5.2: Navigation and Breadcrumbs
1. Implement district navigation breadcrumbs
2. Create district-aware navigation menus
3. Set up cross-district linking
4. Implement search within districts

## Technical Implementation Details

### 1. Hook Integration Points

```php
// Key WordPress hooks to implement
add_action('init', 'register_district_post_types');
add_action('admin_menu', 'create_admin_menus');
add_action('wp_enqueue_scripts', 'enqueue_frontend_assets');
add_action('admin_enqueue_scripts', 'enqueue_admin_assets');
add_filter('the_posts', 'filter_posts_by_district');
add_filter('wp_nav_menu_items', 'add_district_menu_items');
add_action('save_post', 'associate_post_with_district');
add_action('pre_get_posts', 'modify_main_query_for_districts');
```

### 2. Custom Post Meta Integration

```php
// Meta fields for district association
add_meta_box(
    'district_assignment',
    'District Assignment',
    'render_district_meta_box',
    ['post', 'page'],
    'side',
    'high'
);
```

### 3. Rewrite Rules for Clean URLs

```php
// Custom rewrite rules
add_rewrite_rule(
    '^district/([^/]+)/?$',
    'index.php?district_slug=$matches[1]',
    'top'
);

add_rewrite_rule(
    '^district/([^/]+)/posts/?$',
    'index.php?district_slug=$matches[1]&district_section=posts',
    'top'
);
```

### 4. Security Considerations

1. **Data Validation**
   - Sanitize all user inputs
   - Validate district slugs and IDs
   - Implement nonce verification

2. **Permission Management**
   - Create custom capabilities for district management
   - Implement role-based access control
   - Set up user restrictions per district

3. **SQL Injection Prevention**
   - Use prepared statements exclusively
   - Implement parameter binding
   - Validate all database inputs

### 5. Performance Optimization

1. **Caching Strategy**
   - Implement object caching for district data
   - Cache generated menus
   - Use transients for expensive queries

2. **Database Optimization**
   - Proper indexing on frequently queried columns
   - Optimize JOIN queries
   - Implement pagination for large datasets

3. **Asset Optimization**
   - Minify CSS and JavaScript
   - Implement conditional loading
   - Use CDN for static assets

## Testing Strategy

### 1. Unit Testing
- Test all CRUD operations
- Validate data relationships
- Test security functions
- Mock WordPress functions

### 2. Integration Testing
- Test admin interface workflows
- Validate frontend display
- Test menu generation
- Check database integrity

### 3. User Acceptance Testing
- Admin user workflows
- Frontend user experience
- Cross-browser compatibility
- Mobile responsiveness

## Deployment and Maintenance

### 1. Version Control
- Use semantic versioning
- Implement database migration system
- Create rollback procedures
- Document breaking changes

### 2. Documentation
- User manual for administrators
- Developer documentation
- API reference
- Installation guide

### 3. Support and Updates
- Regular security updates
- WordPress compatibility testing
- Feature enhancement roadmap
- Bug tracking system

## Conclusion

This foundation guide provides a comprehensive roadmap for developing the Dakoii Province and District Manager plugin. The modular approach ensures scalability, maintainability, and extensibility while providing robust functionality for managing hierarchical geographic content structures within WordPress.

The implementation should follow WordPress coding standards and best practices throughout development, ensuring compatibility with the broader WordPress ecosystem and future WordPress updates.