<?php
/**
 * Province Form View (Add/Edit)
 * 
 * @package Dakoii_Province_District_Manager
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$is_edit = isset($province) && $province;
$page_title = $is_edit ? __('Edit Province', DAKOII_PDM_TEXT_DOMAIN) : __('Add New Province', DAKOII_PDM_TEXT_DOMAIN);
$form_action = $is_edit ? 'edit_province' : 'add_province';
$nonce_action = $is_edit ? 'dakoii_pdm_edit_province_' . $province->id : 'dakoii_pdm_add_province';
?>

<div class="wrap">
    <h1><?php echo esc_html($page_title); ?></h1>

    <form method="post" class="dakoii-pdm-form">
        <?php wp_nonce_field($nonce_action); ?>
        <input type="hidden" name="action" value="<?php echo esc_attr($form_action); ?>">
        <?php if ($is_edit): ?>
        <input type="hidden" name="province_id" value="<?php echo esc_attr($province->id); ?>">
        <?php endif; ?>

        <table class="form-table" role="presentation">
            <tbody>
                <tr>
                    <th scope="row">
                        <label for="name"><?php _e('Province Name', DAKOII_PDM_TEXT_DOMAIN); ?> <span class="required">*</span></label>
                    </th>
                    <td>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               value="<?php echo $is_edit ? esc_attr($province->name) : ''; ?>" 
                               class="regular-text" 
                               required>
                        <p class="description"><?php _e('Enter the province name.', DAKOII_PDM_TEXT_DOMAIN); ?></p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="slug"><?php _e('Slug', DAKOII_PDM_TEXT_DOMAIN); ?></label>
                    </th>
                    <td>
                        <input type="text" 
                               id="slug" 
                               name="slug" 
                               value="<?php echo $is_edit ? esc_attr($province->slug) : ''; ?>" 
                               class="regular-text">
                        <p class="description"><?php _e('Leave blank to auto-generate from name. Only lowercase letters, numbers, and hyphens allowed.', DAKOII_PDM_TEXT_DOMAIN); ?></p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="description"><?php _e('Description', DAKOII_PDM_TEXT_DOMAIN); ?></label>
                    </th>
                    <td>
                        <textarea id="description" 
                                  name="description" 
                                  rows="4" 
                                  cols="50" 
                                  class="large-text"><?php echo $is_edit ? esc_textarea($province->description) : ''; ?></textarea>
                        <p class="description"><?php _e('Optional description for this province.', DAKOII_PDM_TEXT_DOMAIN); ?></p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="status"><?php _e('Status', DAKOII_PDM_TEXT_DOMAIN); ?></label>
                    </th>
                    <td>
                        <select id="status" name="status">
                            <option value="active" <?php echo (!$is_edit || $province->status === 'active') ? 'selected' : ''; ?>>
                                <?php _e('Active', DAKOII_PDM_TEXT_DOMAIN); ?>
                            </option>
                            <option value="inactive" <?php echo ($is_edit && $province->status === 'inactive') ? 'selected' : ''; ?>>
                                <?php _e('Inactive', DAKOII_PDM_TEXT_DOMAIN); ?>
                            </option>
                        </select>
                        <p class="description"><?php _e('Set the province status.', DAKOII_PDM_TEXT_DOMAIN); ?></p>
                    </td>
                </tr>
            </tbody>
        </table>

        <p class="submit">
            <input type="submit" 
                   class="button-primary" 
                   value="<?php echo $is_edit ? esc_attr__('Update Province', DAKOII_PDM_TEXT_DOMAIN) : esc_attr__('Add Province', DAKOII_PDM_TEXT_DOMAIN); ?>">
            <a href="<?php echo admin_url('admin.php?page=dakoii-pdm-provinces'); ?>" class="button">
                <?php _e('Cancel', DAKOII_PDM_TEXT_DOMAIN); ?>
            </a>
        </p>
    </form>

    <?php if ($is_edit && isset($province->district_count) && $province->district_count > 0): ?>
    <div class="postbox">
        <h3 class="hndle"><?php _e('Associated Districts', DAKOII_PDM_TEXT_DOMAIN); ?></h3>
        <div class="inside">
            <p>
                <?php printf(__('This province has %d district(s).', DAKOII_PDM_TEXT_DOMAIN), $province->district_count); ?>
                <a href="<?php echo admin_url('admin.php?page=dakoii-pdm-districts&province=' . $province->id); ?>">
                    <?php _e('View Districts', DAKOII_PDM_TEXT_DOMAIN); ?>
                </a>
            </p>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
jQuery(document).ready(function($) {
    // Auto-generate slug from name
    $('#name').on('input', function() {
        var name = $(this).val();
        var slug = name.toLowerCase()
                      .replace(/[^a-z0-9\s-]/g, '')
                      .replace(/\s+/g, '-')
                      .replace(/-+/g, '-')
                      .replace(/^-|-$/g, '');
        
        if (!$('#slug').val() || $('#slug').data('auto-generated')) {
            $('#slug').val(slug).data('auto-generated', true);
        }
    });

    // Mark slug as manually edited
    $('#slug').on('input', function() {
        $(this).data('auto-generated', false);
    });
});
</script>

<style>
.required {
    color: #d63638;
}
.form-table th {
    width: 200px;
}
</style>