<?php
/**
 * Database Manager Class
 * 
 * Handles all database operations for the Dakoii Province District Manager plugin
 * 
 * @package Dakoii_Province_District_Manager
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Dakoii_PDM_Database_Manager
{
    /**
     * WordPress database object
     *
     * @var wpdb
     */
    private $wpdb;

    /**
     * Table names
     *
     * @var array
     */
    private $tables;

    /**
     * Constructor
     */
    public function __construct()
    {
        global $wpdb;
        $this->wpdb = $wpdb;
        
        // Define table names
        $this->tables = array(
            'provinces' => $wpdb->prefix . 'dakoii_provinces',
            'districts' => $wpdb->prefix . 'dakoii_districts',
            'district_content' => $wpdb->prefix . 'dakoii_district_content'
        );
    }

    /**
     * Create all plugin tables
     *
     * @return bool
     */
    public function create_tables()
    {
        $this->create_provinces_table();
        $this->create_districts_table();
        $this->create_district_content_table();
        
        return true;
    }

    /**
     * Create provinces table
     */
    private function create_provinces_table()
    {
        $table_name = $this->tables['provinces'];
        
        $charset_collate = $this->wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            name VARCHAR(255) NOT NULL,
            slug VARCHAR(255) NOT NULL UNIQUE,
            description TEXT,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            INDEX idx_slug (slug),
            INDEX idx_status (status)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Create districts table
     */
    private function create_districts_table()
    {
        $table_name = $this->tables['districts'];
        $provinces_table = $this->tables['provinces'];
        
        $charset_collate = $this->wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            province_id BIGINT(20) UNSIGNED NOT NULL,
            name VARCHAR(255) NOT NULL,
            slug VARCHAR(255) NOT NULL UNIQUE,
            description TEXT,
            logo_url VARCHAR(500),
            contact_info JSON,
            menu_settings JSON,
            theme_settings JSON,
            status ENUM('active', 'inactive') DEFAULT 'active',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            FOREIGN KEY (province_id) REFERENCES $provinces_table(id) ON DELETE CASCADE,
            INDEX idx_province_id (province_id),
            INDEX idx_slug (slug),
            INDEX idx_status (status)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Create district content association table
     */
    private function create_district_content_table()
    {
        $table_name = $this->tables['district_content'];
        $districts_table = $this->tables['districts'];
        
        $charset_collate = $this->wpdb->get_charset_collate();
        
        $sql = "CREATE TABLE $table_name (
            id BIGINT(20) UNSIGNED NOT NULL AUTO_INCREMENT,
            district_id BIGINT(20) UNSIGNED NOT NULL,
            post_id BIGINT(20) UNSIGNED NOT NULL,
            content_type ENUM('post', 'page', 'menu') NOT NULL,
            menu_order INT(11) DEFAULT 0,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            FOREIGN KEY (district_id) REFERENCES $districts_table(id) ON DELETE CASCADE,
            FOREIGN KEY (post_id) REFERENCES {$this->wpdb->posts}(ID) ON DELETE CASCADE,
            UNIQUE KEY unique_district_post (district_id, post_id),
            INDEX idx_district_id (district_id),
            INDEX idx_post_id (post_id),
            INDEX idx_content_type (content_type)
        ) $charset_collate;";
        
        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql);
    }

    /**
     * Update tables for plugin updates
     */
    public function update_tables()
    {
        // Check if tables exist and update structure if needed
        $this->create_tables();
    }

    /**
     * Drop all plugin tables
     */
    public function drop_tables()
    {
        $tables = array_reverse($this->tables); // Drop in reverse order due to foreign keys
        
        foreach ($tables as $table) {
            $this->wpdb->query("DROP TABLE IF EXISTS $table");
        }
    }

    /**
     * Check if table exists
     *
     * @param string $table_name
     * @return bool
     */
    public function table_exists($table_name)
    {
        $table = $this->wpdb->get_var($this->wpdb->prepare(
            "SHOW TABLES LIKE %s",
            $table_name
        ));
        
        return $table === $table_name;
    }

    /**
     * Get table name
     *
     * @param string $table_key
     * @return string
     */
    public function get_table_name($table_key)
    {
        return isset($this->tables[$table_key]) ? $this->tables[$table_key] : '';
    }

    /**
     * Get all table names
     *
     * @return array
     */
    public function get_all_table_names()
    {
        return $this->tables;
    }

    /**
     * Validate database integrity
     *
     * @return array
     */
    public function validate_integrity()
    {
        $results = array();
        
        foreach ($this->tables as $key => $table) {
            $results[$key] = $this->table_exists($table);
        }
        
        return $results;
    }

    /**
     * Get database statistics
     *
     * @return array
     */
    public function get_statistics()
    {
        $stats = array();
        
        // Count provinces
        $stats['provinces'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->tables['provinces']} WHERE status = 'active'"
        );
        
        // Count districts
        $stats['districts'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->tables['districts']} WHERE status = 'active'"
        );
        
        // Count district content associations
        $stats['content_associations'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->tables['district_content']}"
        );
        
        // Get average districts per province
        $stats['avg_districts_per_province'] = $stats['provinces'] > 0 
            ? round($stats['districts'] / $stats['provinces'], 2) 
            : 0;
        
        return $stats;
    }

    /**
     * Clean orphaned records
     *
     * @return int Number of cleaned records
     */
    public function clean_orphaned_records()
    {
        $cleaned = 0;
        
        // Clean orphaned districts (provinces that don't exist)
        $cleaned += $this->wpdb->query("
            DELETE d FROM {$this->tables['districts']} d
            LEFT JOIN {$this->tables['provinces']} p ON d.province_id = p.id
            WHERE p.id IS NULL
        ");
        
        // Clean orphaned content associations (districts that don't exist)
        $cleaned += $this->wpdb->query("
            DELETE dc FROM {$this->tables['district_content']} dc
            LEFT JOIN {$this->tables['districts']} d ON dc.district_id = d.id
            WHERE d.id IS NULL
        ");
        
        // Clean orphaned content associations (posts that don't exist)
        $cleaned += $this->wpdb->query("
            DELETE dc FROM {$this->tables['district_content']} dc
            LEFT JOIN {$this->wpdb->posts} p ON dc.post_id = p.ID
            WHERE p.ID IS NULL
        ");
        
        return $cleaned;
    }

    /**
     * Backup plugin data
     *
     * @return array
     */
    public function backup_data()
    {
        $backup = array();
        
        // Backup provinces
        $backup['provinces'] = $this->wpdb->get_results(
            "SELECT * FROM {$this->tables['provinces']}",
            ARRAY_A
        );
        
        // Backup districts
        $backup['districts'] = $this->wpdb->get_results(
            "SELECT * FROM {$this->tables['districts']}",
            ARRAY_A
        );
        
        // Backup district content
        $backup['district_content'] = $this->wpdb->get_results(
            "SELECT * FROM {$this->tables['district_content']}",
            ARRAY_A
        );
        
        $backup['timestamp'] = current_time('mysql');
        $backup['version'] = DAKOII_PDM_VERSION;
        
        return $backup;
    }

    /**
     * Restore plugin data from backup
     *
     * @param array $backup_data
     * @return bool
     */
    public function restore_data($backup_data)
    {
        if (!is_array($backup_data) || empty($backup_data)) {
            return false;
        }
        
        try {
            $this->wpdb->query('START TRANSACTION');
            
            // Clear existing data
            $this->wpdb->query("DELETE FROM {$this->tables['district_content']}");
            $this->wpdb->query("DELETE FROM {$this->tables['districts']}");
            $this->wpdb->query("DELETE FROM {$this->tables['provinces']}");
            
            // Restore provinces
            if (!empty($backup_data['provinces'])) {
                foreach ($backup_data['provinces'] as $province) {
                    $this->wpdb->insert($this->tables['provinces'], $province);
                }
            }
            
            // Restore districts
            if (!empty($backup_data['districts'])) {
                foreach ($backup_data['districts'] as $district) {
                    $this->wpdb->insert($this->tables['districts'], $district);
                }
            }
            
            // Restore district content
            if (!empty($backup_data['district_content'])) {
                foreach ($backup_data['district_content'] as $content) {
                    $this->wpdb->insert($this->tables['district_content'], $content);
                }
            }
            
            $this->wpdb->query('COMMIT');
            return true;
            
        } catch (Exception $e) {
            $this->wpdb->query('ROLLBACK');
            error_log('Dakoii PDM: Restore failed - ' . $e->getMessage());
            return false;
        }
    }
}