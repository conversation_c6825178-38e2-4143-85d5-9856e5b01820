<?php
/**
 * Dashboard View
 * 
 * @package Dakoii_Province_District_Manager
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}
?>

<div class="wrap dakoii-pdm-dashboard">
    <h1><?php _e('Dakoii Province & District Manager', DAKOII_PDM_TEXT_DOMAIN); ?></h1>
    
    <div class="dakoii-pdm-stats-grid">
        <div class="dakoii-pdm-stat-card">
            <h3><?php echo esc_html($province_stats['active']); ?></h3>
            <p><?php _e('Active Provinces', DAKOII_PDM_TEXT_DOMAIN); ?></p>
        </div>
        
        <div class="dakoii-pdm-stat-card">
            <h3><?php echo esc_html($district_stats['active']); ?></h3>
            <p><?php _e('Active Districts', DAKOII_PDM_TEXT_DOMAIN); ?></p>
        </div>
        
        <div class="dakoii-pdm-stat-card">
            <h3><?php echo esc_html($content_stats['total_associations']); ?></h3>
            <p><?php _e('Content Associations', DAKOII_PDM_TEXT_DOMAIN); ?></p>
        </div>
        
        <div class="dakoii-pdm-stat-card">
            <h3><?php echo isset($district_stats['by_province']) ? count($district_stats['by_province']) : 0; ?></h3>
            <p><?php _e('Provinces with Districts', DAKOII_PDM_TEXT_DOMAIN); ?></p>
        </div>
    </div>

    <div class="dakoii-pdm-dashboard-content">
        <div class="dakoii-pdm-dashboard-main">
            <h2><?php _e('Quick Actions', DAKOII_PDM_TEXT_DOMAIN); ?></h2>
            <p>
                <a href="<?php echo admin_url('admin.php?page=dakoii-pdm-add-province'); ?>" class="button button-primary">
                    <?php _e('Add New Province', DAKOII_PDM_TEXT_DOMAIN); ?>
                </a>
                <a href="<?php echo admin_url('admin.php?page=dakoii-pdm-add-district'); ?>" class="button button-primary">
                    <?php _e('Add New District', DAKOII_PDM_TEXT_DOMAIN); ?>
                </a>
                <a href="<?php echo admin_url('admin.php?page=dakoii-pdm-provinces'); ?>" class="button">
                    <?php _e('Manage Provinces', DAKOII_PDM_TEXT_DOMAIN); ?>
                </a>
                <a href="<?php echo admin_url('admin.php?page=dakoii-pdm-districts'); ?>" class="button">
                    <?php _e('Manage Districts', DAKOII_PDM_TEXT_DOMAIN); ?>
                </a>
            </p>

            <?php if (!empty($district_stats['by_province'])): ?>
            <h3><?php _e('Districts by Province', DAKOII_PDM_TEXT_DOMAIN); ?></h3>
            <table class="wp-list-table widefat fixed striped">
                <thead>
                    <tr>
                        <th><?php _e('Province', DAKOII_PDM_TEXT_DOMAIN); ?></th>
                        <th><?php _e('Districts', DAKOII_PDM_TEXT_DOMAIN); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php foreach ($district_stats['by_province'] as $stat): ?>
                    <tr>
                        <td><?php echo esc_html($stat['province_name']); ?></td>
                        <td><?php echo esc_html($stat['district_count']); ?></td>
                    </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
            <?php endif; ?>
        </div>

        <div class="dakoii-pdm-dashboard-sidebar">
            <div class="postbox">
                <h3 class="hndle"><?php _e('System Information', DAKOII_PDM_TEXT_DOMAIN); ?></h3>
                <div class="inside">
                    <p><strong><?php _e('Plugin Version:', DAKOII_PDM_TEXT_DOMAIN); ?></strong> <?php echo DAKOII_PDM_VERSION; ?></p>
                    <p><strong><?php _e('WordPress Version:', DAKOII_PDM_TEXT_DOMAIN); ?></strong> <?php echo get_bloginfo('version'); ?></p>
                    <p><strong><?php _e('PHP Version:', DAKOII_PDM_TEXT_DOMAIN); ?></strong> <?php echo PHP_VERSION; ?></p>
                </div>
            </div>

            <div class="postbox">
                <h3 class="hndle"><?php _e('Support', DAKOII_PDM_TEXT_DOMAIN); ?></h3>
                <div class="inside">
                    <p><?php _e('Need help? Contact us:', DAKOII_PDM_TEXT_DOMAIN); ?></p>
                    <p>
                        <strong><?php _e('Website:', DAKOII_PDM_TEXT_DOMAIN); ?></strong> 
                        <a href="https://www.dakoiims.com" target="_blank">www.dakoiims.com</a>
                    </p>
                    <p>
                        <strong><?php _e('Email:', DAKOII_PDM_TEXT_DOMAIN); ?></strong> 
                        <a href="mailto:<EMAIL>"><EMAIL></a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.dakoii-pdm-dashboard-content {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: 20px;
    margin-top: 20px;
}

.dakoii-pdm-dashboard-main {
    background: #fff;
    padding: 20px;
    border: 1px solid #ccd0d4;
    border-radius: 4px;
}

.dakoii-pdm-dashboard-sidebar .postbox {
    margin-bottom: 20px;
}

@media (max-width: 768px) {
    .dakoii-pdm-dashboard-content {
        grid-template-columns: 1fr;
    }
}
</style>