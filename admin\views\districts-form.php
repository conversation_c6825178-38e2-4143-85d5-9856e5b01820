<?php
/**
 * District Form View (Add/Edit)
 * 
 * @package Dakoii_Province_District_Manager
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$is_edit = isset($district) && $district;
$page_title = $is_edit ? __('Edit District', DAKOII_PDM_TEXT_DOMAIN) : __('Add New District', DAKOII_PDM_TEXT_DOMAIN);
$form_action = $is_edit ? 'edit_district' : 'add_district';
$nonce_action = $is_edit ? 'dakoii_pdm_edit_district_' . $district->id : 'dakoii_pdm_add_district';
?>

<div class="wrap">
    <h1><?php echo esc_html($page_title); ?></h1>

    <form method="post" class="dakoii-pdm-form">
        <?php wp_nonce_field($nonce_action); ?>
        <input type="hidden" name="action" value="<?php echo esc_attr($form_action); ?>">
        <?php if ($is_edit): ?>
        <input type="hidden" name="district_id" value="<?php echo esc_attr($district->id); ?>">
        <?php endif; ?>

        <table class="form-table" role="presentation">
            <tbody>
                <tr>
                    <th scope="row">
                        <label for="province_id"><?php _e('Province', DAKOII_PDM_TEXT_DOMAIN); ?> <span class="required">*</span></label>
                    </th>
                    <td>
                        <select id="province_id" name="province_id" required>
                            <option value=""><?php _e('Select Province', DAKOII_PDM_TEXT_DOMAIN); ?></option>
                            <?php foreach ($provinces as $id => $name): ?>
                            <option value="<?php echo esc_attr($id); ?>" <?php echo ($is_edit && $district->province_id == $id) ? 'selected' : ''; ?>>
                                <?php echo esc_html($name); ?>
                            </option>
                            <?php endforeach; ?>
                        </select>
                        <p class="description"><?php _e('Select the province for this district.', DAKOII_PDM_TEXT_DOMAIN); ?></p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="name"><?php _e('District Name', DAKOII_PDM_TEXT_DOMAIN); ?> <span class="required">*</span></label>
                    </th>
                    <td>
                        <input type="text" 
                               id="name" 
                               name="name" 
                               value="<?php echo $is_edit ? esc_attr($district->name) : ''; ?>" 
                               class="regular-text" 
                               required>
                        <p class="description"><?php _e('Enter the district name.', DAKOII_PDM_TEXT_DOMAIN); ?></p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="slug"><?php _e('Slug', DAKOII_PDM_TEXT_DOMAIN); ?></label>
                    </th>
                    <td>
                        <input type="text" 
                               id="slug" 
                               name="slug" 
                               value="<?php echo $is_edit ? esc_attr($district->slug) : ''; ?>" 
                               class="regular-text">
                        <p class="description"><?php _e('Leave blank to auto-generate from name. Only lowercase letters, numbers, and hyphens allowed.', DAKOII_PDM_TEXT_DOMAIN); ?></p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="description"><?php _e('Description', DAKOII_PDM_TEXT_DOMAIN); ?></label>
                    </th>
                    <td>
                        <textarea id="description" 
                                  name="description" 
                                  rows="4" 
                                  cols="50" 
                                  class="large-text"><?php echo $is_edit ? esc_textarea($district->description) : ''; ?></textarea>
                        <p class="description"><?php _e('Optional description for this district.', DAKOII_PDM_TEXT_DOMAIN); ?></p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="logo_url"><?php _e('Logo URL', DAKOII_PDM_TEXT_DOMAIN); ?></label>
                    </th>
                    <td>
                        <input type="url" 
                               id="logo_url" 
                               name="logo_url" 
                               value="<?php echo $is_edit ? esc_attr($district->logo_url) : ''; ?>" 
                               class="regular-text">
                        <button type="button" class="button" id="upload-logo"><?php _e('Upload Logo', DAKOII_PDM_TEXT_DOMAIN); ?></button>
                        <p class="description"><?php _e('Optional logo for the district.', DAKOII_PDM_TEXT_DOMAIN); ?></p>
                        <?php if ($is_edit && !empty($district->logo_url)): ?>
                        <div id="logo-preview" style="margin-top: 10px;">
                            <img src="<?php echo esc_url($district->logo_url); ?>" alt="<?php echo esc_attr($district->name); ?>" style="max-width: 150px; max-height: 150px;">
                        </div>
                        <?php else: ?>
                        <div id="logo-preview" style="margin-top: 10px; display: none;"></div>
                        <?php endif; ?>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label><?php _e('Contact Information', DAKOII_PDM_TEXT_DOMAIN); ?></label>
                    </th>
                    <td>
                        <div class="contact-info-fields">
                            <p>
                                <label for="contact_email"><?php _e('Email:', DAKOII_PDM_TEXT_DOMAIN); ?></label><br>
                                <input type="email" 
                                       id="contact_email" 
                                       name="contact_info[email]" 
                                       value="<?php echo $is_edit && isset($district->contact_info['email']) ? esc_attr($district->contact_info['email']) : ''; ?>" 
                                       class="regular-text">
                            </p>
                            <p>
                                <label for="contact_phone"><?php _e('Phone:', DAKOII_PDM_TEXT_DOMAIN); ?></label><br>
                                <input type="tel" 
                                       id="contact_phone" 
                                       name="contact_info[phone]" 
                                       value="<?php echo $is_edit && isset($district->contact_info['phone']) ? esc_attr($district->contact_info['phone']) : ''; ?>" 
                                       class="regular-text">
                            </p>
                            <p>
                                <label for="contact_address"><?php _e('Address:', DAKOII_PDM_TEXT_DOMAIN); ?></label><br>
                                <textarea id="contact_address" 
                                          name="contact_info[address]" 
                                          rows="3" 
                                          cols="50" 
                                          class="large-text"><?php echo $is_edit && isset($district->contact_info['address']) ? esc_textarea($district->contact_info['address']) : ''; ?></textarea>
                            </p>
                            <p>
                                <label for="contact_website"><?php _e('Website:', DAKOII_PDM_TEXT_DOMAIN); ?></label><br>
                                <input type="url" 
                                       id="contact_website" 
                                       name="contact_info[website]" 
                                       value="<?php echo $is_edit && isset($district->contact_info['website']) ? esc_attr($district->contact_info['website']) : ''; ?>" 
                                       class="regular-text">
                            </p>
                        </div>
                        <p class="description"><?php _e('Optional contact information for this district.', DAKOII_PDM_TEXT_DOMAIN); ?></p>
                    </td>
                </tr>

                <tr>
                    <th scope="row">
                        <label for="status"><?php _e('Status', DAKOII_PDM_TEXT_DOMAIN); ?></label>
                    </th>
                    <td>
                        <select id="status" name="status">
                            <option value="active" <?php echo (!$is_edit || $district->status === 'active') ? 'selected' : ''; ?>>
                                <?php _e('Active', DAKOII_PDM_TEXT_DOMAIN); ?>
                            </option>
                            <option value="inactive" <?php echo ($is_edit && $district->status === 'inactive') ? 'selected' : ''; ?>>
                                <?php _e('Inactive', DAKOII_PDM_TEXT_DOMAIN); ?>
                            </option>
                        </select>
                        <p class="description"><?php _e('Set the district status.', DAKOII_PDM_TEXT_DOMAIN); ?></p>
                    </td>
                </tr>
            </tbody>
        </table>

        <p class="submit">
            <input type="submit" 
                   class="button-primary" 
                   value="<?php echo $is_edit ? esc_attr__('Update District', DAKOII_PDM_TEXT_DOMAIN) : esc_attr__('Add District', DAKOII_PDM_TEXT_DOMAIN); ?>">
            <a href="<?php echo admin_url('admin.php?page=dakoii-pdm-districts'); ?>" class="button">
                <?php _e('Cancel', DAKOII_PDM_TEXT_DOMAIN); ?>
            </a>
        </p>
    </form>

    <?php if ($is_edit && isset($district->content_count) && $district->content_count > 0): ?>
    <div class="postbox">
        <h3 class="hndle"><?php _e('Associated Content', DAKOII_PDM_TEXT_DOMAIN); ?></h3>
        <div class="inside">
            <p>
                <?php printf(__('This district has %d content item(s).', DAKOII_PDM_TEXT_DOMAIN), $district->content_count); ?>
                <a href="<?php echo home_url('/district/' . $district->slug); ?>" target="_blank">
                    <?php _e('View District', DAKOII_PDM_TEXT_DOMAIN); ?>
                </a>
            </p>
        </div>
    </div>
    <?php endif; ?>
</div>

<script>
jQuery(document).ready(function($) {
    // Auto-generate slug from name
    $('#name').on('input', function() {
        var name = $(this).val();
        var slug = name.toLowerCase()
                      .replace(/[^a-z0-9\s-]/g, '')
                      .replace(/\s+/g, '-')
                      .replace(/-+/g, '-')
                      .replace(/^-|-$/g, '');
        
        if (!$('#slug').val() || $('#slug').data('auto-generated')) {
            $('#slug').val(slug).data('auto-generated', true);
        }
    });

    // Mark slug as manually edited
    $('#slug').on('input', function() {
        $(this).data('auto-generated', false);
    });

    // Media uploader for logo
    var mediaUploader;
    
    $('#upload-logo').click(function(e) {
        e.preventDefault();
        
        if (mediaUploader) {
            mediaUploader.open();
            return;
        }
        
        mediaUploader = wp.media.frames.file_frame = wp.media({
            title: '<?php esc_js_e('Choose Logo', DAKOII_PDM_TEXT_DOMAIN); ?>',
            button: {
                text: '<?php esc_js_e('Choose Logo', DAKOII_PDM_TEXT_DOMAIN); ?>'
            },
            multiple: false
        });
        
        mediaUploader.on('select', function() {
            var attachment = mediaUploader.state().get('selection').first().toJSON();
            $('#logo_url').val(attachment.url);
            $('#logo-preview').html('<img src="' + attachment.url + '" style="max-width: 150px; max-height: 150px;">').show();
        });
        
        mediaUploader.open();
    });

    // Show/hide logo preview
    $('#logo_url').on('input', function() {
        var url = $(this).val();
        if (url) {
            $('#logo-preview').html('<img src="' + url + '" style="max-width: 150px; max-height: 150px;">').show();
        } else {
            $('#logo-preview').hide();
        }
    });
});
</script>

<style>
.required {
    color: #d63638;
}
.form-table th {
    width: 200px;
}
.contact-info-fields p {
    margin-bottom: 15px;
}
.contact-info-fields label {
    font-weight: 600;
    display: inline-block;
    margin-bottom: 5px;
}
</style>