<?php
/**
 * Plugin Name: Dakoii Province and District Manager
 * Plugin URI: https://www.dakoiims.com
 * Description: A comprehensive WordPress plugin for managing provinces and districts with hierarchical content management capabilities. Each district functions as a mini-website with its own content and menu structure.
 * Version: 1.0.0
 * Author: <PERSON><PERSON>
 * Author URI: https://www.dakoiims.com
 * Text Domain: dakoii-prov-district-manager
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.3
 * Requires PHP: 7.4
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Network: false
 *
 * @package Dakoii_Province_District_Manager
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('DAKOII_PDM_VERSION', '1.0.0');
define('DAKOII_PDM_PLUGIN_FILE', __FILE__);
define('DAKOII_PDM_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('DAKOII_PDM_PLUGIN_URL', plugin_dir_url(__FILE__));
define('DAKOII_PDM_PLUGIN_BASENAME', plugin_basename(__FILE__));
define('DAKOII_PDM_TEXT_DOMAIN', 'dakoii-prov-district-manager');

/**
 * Main plugin class
 */
class Dakoii_Province_District_Manager
{
    /**
     * Plugin instance
     *
     * @var Dakoii_Province_District_Manager
     */
    private static $instance = null;

    /**
     * Plugin version
     *
     * @var string
     */
    public $version = DAKOII_PDM_VERSION;

    /**
     * Database manager instance
     *
     * @var Dakoii_PDM_Database_Manager
     */
    public $database_manager;

    /**
     * Province manager instance
     *
     * @var Dakoii_PDM_Province_Manager
     */
    public $province_manager;

    /**
     * District manager instance
     *
     * @var Dakoii_PDM_District_Manager
     */
    public $district_manager;

    /**
     * Content manager instance
     *
     * @var Dakoii_PDM_Content_Manager
     */
    public $content_manager;

    /**
     * Admin interface instance
     *
     * @var Dakoii_PDM_Admin_Interface
     */
    public $admin_interface;

    /**
     * Frontend handler instance
     *
     * @var Dakoii_PDM_Frontend_Handler
     */
    public $frontend_handler;

    /**
     * Get plugin instance
     *
     * @return Dakoii_Province_District_Manager
     */
    public static function get_instance()
    {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct()
    {
        $this->init_hooks();
        $this->load_dependencies();
        $this->init_components();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks()
    {
        // Plugin lifecycle hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        register_uninstall_hook(__FILE__, array('Dakoii_Province_District_Manager', 'uninstall'));

        // WordPress hooks
        add_action('plugins_loaded', array($this, 'load_textdomain'));
        add_action('init', array($this, 'init'));
        add_action('admin_init', array($this, 'admin_init'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_scripts'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));
    }

    /**
     * Load plugin dependencies
     */
    private function load_dependencies()
    {
        // Core includes
        require_once DAKOII_PDM_PLUGIN_DIR . 'includes/class-database-manager.php';
        require_once DAKOII_PDM_PLUGIN_DIR . 'includes/class-province-manager.php';
        require_once DAKOII_PDM_PLUGIN_DIR . 'includes/class-district-manager.php';
        require_once DAKOII_PDM_PLUGIN_DIR . 'includes/class-content-manager.php';
        require_once DAKOII_PDM_PLUGIN_DIR . 'includes/class-admin-interface.php';
        require_once DAKOII_PDM_PLUGIN_DIR . 'includes/class-frontend-handler.php';
    }

    /**
     * Initialize plugin components
     */
    private function init_components()
    {
        $this->database_manager = new Dakoii_PDM_Database_Manager();
        $this->province_manager = new Dakoii_PDM_Province_Manager();
        $this->district_manager = new Dakoii_PDM_District_Manager();
        $this->content_manager = new Dakoii_PDM_Content_Manager();
        $this->admin_interface = new Dakoii_PDM_Admin_Interface();
        $this->frontend_handler = new Dakoii_PDM_Frontend_Handler();
    }

    /**
     * Plugin activation
     */
    public function activate()
    {
        // Create database tables
        if ($this->database_manager) {
            $this->database_manager->create_tables();
        }

        // Set default options
        $this->set_default_options();

        // Flush rewrite rules
        flush_rewrite_rules();

        // Log activation
        error_log('Dakoii Province District Manager activated successfully');
    }

    /**
     * Plugin deactivation
     */
    public function deactivate()
    {
        // Flush rewrite rules
        flush_rewrite_rules();

        // Log deactivation
        error_log('Dakoii Province District Manager deactivated');
    }

    /**
     * Plugin uninstall
     */
    public static function uninstall()
    {
        // Remove database tables
        $database_manager = new Dakoii_PDM_Database_Manager();
        $database_manager->drop_tables();

        // Remove options
        delete_option('dakoii_pdm_version');
        delete_option('dakoii_pdm_settings');

        // Clear any cached data
        wp_cache_flush();

        // Log uninstall
        error_log('Dakoii Province District Manager uninstalled');
    }

    /**
     * Load plugin text domain
     */
    public function load_textdomain()
    {
        load_plugin_textdomain(
            DAKOII_PDM_TEXT_DOMAIN,
            false,
            dirname(DAKOII_PDM_PLUGIN_BASENAME) . '/languages'
        );
    }

    /**
     * Initialize plugin
     */
    public function init()
    {
        // Register custom post types if needed
        $this->register_post_types();

        // Register custom taxonomies if needed
        $this->register_taxonomies();

        // Add rewrite rules
        $this->add_rewrite_rules();
    }

    /**
     * Admin initialization
     */
    public function admin_init()
    {
        // Check if database needs updates
        $this->check_database_version();
    }

    /**
     * Enqueue frontend scripts and styles
     */
    public function enqueue_frontend_scripts()
    {
        wp_enqueue_style(
            'dakoii-pdm-frontend',
            DAKOII_PDM_PLUGIN_URL . 'frontend/css/frontend-styles.css',
            array(),
            DAKOII_PDM_VERSION
        );

        wp_enqueue_script(
            'dakoii-pdm-frontend',
            DAKOII_PDM_PLUGIN_URL . 'frontend/js/frontend-scripts.js',
            array('jquery'),
            DAKOII_PDM_VERSION,
            true
        );

        // Localize script for AJAX
        wp_localize_script('dakoii-pdm-frontend', 'dakoii_pdm_ajax', array(
            'ajax_url' => admin_url('admin-ajax.php'),
            'nonce' => wp_create_nonce('dakoii_pdm_nonce'),
            'text_domain' => DAKOII_PDM_TEXT_DOMAIN
        ));
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook)
    {
        // Only load on our admin pages
        if (strpos($hook, 'dakoii-') !== false) {
            wp_enqueue_style(
                'dakoii-pdm-admin',
                DAKOII_PDM_PLUGIN_URL . 'admin/css/admin-styles.css',
                array(),
                DAKOII_PDM_VERSION
            );

            wp_enqueue_script(
                'dakoii-pdm-admin',
                DAKOII_PDM_PLUGIN_URL . 'admin/js/admin-scripts.js',
                array('jquery', 'wp-util'),
                DAKOII_PDM_VERSION,
                true
            );

            // Localize admin script
            wp_localize_script('dakoii-pdm-admin', 'dakoii_pdm_admin', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('dakoii_pdm_admin_nonce'),
                'text_domain' => DAKOII_PDM_TEXT_DOMAIN,
                'confirm_delete' => __('Are you sure you want to delete this item?', DAKOII_PDM_TEXT_DOMAIN)
            ));
        }
    }

    /**
     * Set default plugin options
     */
    private function set_default_options()
    {
        add_option('dakoii_pdm_version', DAKOII_PDM_VERSION);
        add_option('dakoii_pdm_settings', array(
            'enable_district_menus' => true,
            'show_district_breadcrumbs' => true,
            'district_posts_per_page' => 10,
            'enable_district_search' => true
        ));
    }

    /**
     * Register custom post types
     */
    private function register_post_types()
    {
        // Custom post types will be registered here if needed
        // Currently using standard WordPress posts and pages
    }

    /**
     * Register custom taxonomies
     */
    private function register_taxonomies()
    {
        // Custom taxonomies will be registered here if needed
    }

    /**
     * Add custom rewrite rules
     */
    private function add_rewrite_rules()
    {
        // District pages
        add_rewrite_rule(
            '^district/([^/]+)/?$',
            'index.php?district_slug=$matches[1]',
            'top'
        );

        // District posts
        add_rewrite_rule(
            '^district/([^/]+)/posts/?$',
            'index.php?district_slug=$matches[1]&district_section=posts',
            'top'
        );

        // District pages
        add_rewrite_rule(
            '^district/([^/]+)/pages/?$',
            'index.php?district_slug=$matches[1]&district_section=pages',
            'top'
        );

        // Specific district post
        add_rewrite_rule(
            '^district/([^/]+)/post/([^/]+)/?$',
            'index.php?district_slug=$matches[1]&post_name=$matches[2]',
            'top'
        );

        // Specific district page
        add_rewrite_rule(
            '^district/([^/]+)/page/([^/]+)/?$',
            'index.php?district_slug=$matches[1]&pagename=$matches[2]',
            'top'
        );

        // Add query vars
        add_filter('query_vars', array($this, 'add_query_vars'));
    }

    /**
     * Add custom query variables
     */
    public function add_query_vars($vars)
    {
        $vars[] = 'district_slug';
        $vars[] = 'district_section';
        return $vars;
    }

    /**
     * Check database version and update if needed
     */
    private function check_database_version()
    {
        $current_version = get_option('dakoii_pdm_version', '0.0.0');
        
        if (version_compare($current_version, DAKOII_PDM_VERSION, '<')) {
            // Run database updates
            $this->database_manager->update_tables();
            update_option('dakoii_pdm_version', DAKOII_PDM_VERSION);
        }
    }

    /**
     * Get plugin version
     */
    public function get_version()
    {
        return $this->version;
    }
}

/**
 * Initialize the plugin
 */
function dakoii_pdm_init()
{
    return Dakoii_Province_District_Manager::get_instance();
}

// Start the plugin
dakoii_pdm_init();

/**
 * Helper function to get plugin instance
 */
function dakoii_pdm()
{
    return Dakoii_Province_District_Manager::get_instance();
}