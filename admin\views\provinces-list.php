<?php
/**
 * Provinces List View
 * 
 * @package Dakoii_Province_District_Manager
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$base_url = admin_url('admin.php?page=dakoii-pdm-provinces');
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php _e('Provinces', DAKOII_PDM_TEXT_DOMAIN); ?></h1>
    <a href="<?php echo admin_url('admin.php?page=dakoii-pdm-add-province'); ?>" class="page-title-action">
        <?php _e('Add New', DAKOII_PDM_TEXT_DOMAIN); ?>
    </a>

    <?php if (!empty($search)): ?>
    <span class="subtitle"><?php printf(__('Search results for "%s"', DAKOII_PDM_TEXT_DOMAIN), esc_html($search)); ?></span>
    <?php endif; ?>

    <form method="get" class="search-form">
        <input type="hidden" name="page" value="dakoii-pdm-provinces">
        <p class="search-box">
            <label class="screen-reader-text" for="province-search-input"><?php _e('Search Provinces:', DAKOII_PDM_TEXT_DOMAIN); ?></label>
            <input type="search" id="province-search-input" name="s" value="<?php echo esc_attr($search); ?>">
            <input type="submit" id="search-submit" class="button" value="<?php esc_attr_e('Search Provinces', DAKOII_PDM_TEXT_DOMAIN); ?>">
        </p>
    </form>

    <div class="tablenav top">
        <?php 
        $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $per_page = 20;
        echo dakoii_pdm()->admin_interface->get_pagination($current_page, $total_items, $per_page, $base_url);
        ?>
    </div>

    <table class="wp-list-table widefat fixed striped">
        <thead>
            <tr>
                <th scope="col" class="manage-column column-name column-primary">
                    <?php _e('Name', DAKOII_PDM_TEXT_DOMAIN); ?>
                </th>
                <th scope="col" class="manage-column column-slug">
                    <?php _e('Slug', DAKOII_PDM_TEXT_DOMAIN); ?>
                </th>
                <th scope="col" class="manage-column column-districts">
                    <?php _e('Districts', DAKOII_PDM_TEXT_DOMAIN); ?>
                </th>
                <th scope="col" class="manage-column column-status">
                    <?php _e('Status', DAKOII_PDM_TEXT_DOMAIN); ?>
                </th>
                <th scope="col" class="manage-column column-date">
                    <?php _e('Date', DAKOII_PDM_TEXT_DOMAIN); ?>
                </th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($provinces)): ?>
            <tr>
                <td colspan="5" class="no-items">
                    <?php _e('No provinces found.', DAKOII_PDM_TEXT_DOMAIN); ?>
                </td>
            </tr>
            <?php else: ?>
                <?php foreach ($provinces as $province): ?>
                <tr>
                    <td class="column-name column-primary">
                        <strong>
                            <a href="<?php echo admin_url('admin.php?page=dakoii-pdm-provinces&action=edit&id=' . $province->id); ?>">
                                <?php echo esc_html($province->name); ?>
                            </a>
                        </strong>
                        <div class="row-actions">
                            <span class="edit">
                                <a href="<?php echo admin_url('admin.php?page=dakoii-pdm-provinces&action=edit&id=' . $province->id); ?>">
                                    <?php _e('Edit', DAKOII_PDM_TEXT_DOMAIN); ?>
                                </a> |
                            </span>
                            <span class="delete">
                                <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=dakoii-pdm-provinces&action=delete_province&id=' . $province->id), 'dakoii_pdm_delete_province_' . $province->id); ?>" 
                                   class="dakoii-pdm-delete">
                                    <?php _e('Delete', DAKOII_PDM_TEXT_DOMAIN); ?>
                                </a>
                            </span>
                            <?php if (isset($province->district_count) && $province->district_count > 0): ?>
                            | <span class="view">
                                <a href="<?php echo admin_url('admin.php?page=dakoii-pdm-districts&province=' . $province->id); ?>">
                                    <?php _e('View Districts', DAKOII_PDM_TEXT_DOMAIN); ?>
                                </a>
                            </span>
                            <?php endif; ?>
                        </div>
                        <button type="button" class="toggle-row"><span class="screen-reader-text"><?php _e('Show more details', DAKOII_PDM_TEXT_DOMAIN); ?></span></button>
                    </td>
                    <td class="column-slug" data-colname="<?php esc_attr_e('Slug', DAKOII_PDM_TEXT_DOMAIN); ?>">
                        <code><?php echo esc_html($province->slug); ?></code>
                    </td>
                    <td class="column-districts" data-colname="<?php esc_attr_e('Districts', DAKOII_PDM_TEXT_DOMAIN); ?>">
                        <?php 
                        if (isset($province->district_count)) {
                            if ($province->district_count > 0) {
                                echo '<a href="' . admin_url('admin.php?page=dakoii-pdm-districts&province=' . $province->id) . '">';
                                echo esc_html($province->district_count);
                                echo '</a>';
                            } else {
                                echo '<span style="color: #999;">0</span>';
                            }
                        }
                        ?>
                    </td>
                    <td class="column-status" data-colname="<?php esc_attr_e('Status', DAKOII_PDM_TEXT_DOMAIN); ?>">
                        <span class="status-<?php echo esc_attr($province->status); ?>">
                            <?php echo $province->status === 'active' ? __('Active', DAKOII_PDM_TEXT_DOMAIN) : __('Inactive', DAKOII_PDM_TEXT_DOMAIN); ?>
                        </span>
                    </td>
                    <td class="column-date" data-colname="<?php esc_attr_e('Date', DAKOII_PDM_TEXT_DOMAIN); ?>">
                        <?php echo esc_html($province->created_at_formatted); ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>

    <div class="tablenav bottom">
        <?php echo dakoii_pdm()->admin_interface->get_pagination($current_page, $total_items, $per_page, $base_url); ?>
    </div>
</div>

<style>
.status-active {
    color: #007cba;
    font-weight: 600;
}
.status-inactive {
    color: #999;
}
</style>