<?php
/**
 * Districts List View
 * 
 * @package Dakoii_Province_District_Manager
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

$base_url = admin_url('admin.php?page=dakoii-pdm-districts');
?>

<div class="wrap">
    <h1 class="wp-heading-inline"><?php _e('Districts', DAKOII_PDM_TEXT_DOMAIN); ?></h1>
    <a href="<?php echo admin_url('admin.php?page=dakoii-pdm-add-district'); ?>" class="page-title-action">
        <?php _e('Add New', DAKOII_PDM_TEXT_DOMAIN); ?>
    </a>

    <?php if (!empty($search)): ?>
    <span class="subtitle"><?php printf(__('Search results for "%s"', DAKOII_PDM_TEXT_DOMAIN), esc_html($search)); ?></span>
    <?php endif; ?>

    <div class="tablenav top">
        <div class="alignleft actions">
            <form method="get" style="display: inline-block;">
                <input type="hidden" name="page" value="dakoii-pdm-districts">
                <select name="province" id="filter-by-province">
                    <option value=""><?php _e('All Provinces', DAKOII_PDM_TEXT_DOMAIN); ?></option>
                    <?php foreach ($provinces as $id => $name): ?>
                    <option value="<?php echo esc_attr($id); ?>" <?php selected($province_filter, $id); ?>>
                        <?php echo esc_html($name); ?>
                    </option>
                    <?php endforeach; ?>
                </select>
                <input type="submit" class="button" value="<?php esc_attr_e('Filter', DAKOII_PDM_TEXT_DOMAIN); ?>">
            </form>
        </div>

        <div class="alignright">
            <form method="get" class="search-form" style="display: inline-block;">
                <input type="hidden" name="page" value="dakoii-pdm-districts">
                <?php if ($province_filter): ?>
                <input type="hidden" name="province" value="<?php echo esc_attr($province_filter); ?>">
                <?php endif; ?>
                <p class="search-box" style="margin: 0;">
                    <label class="screen-reader-text" for="district-search-input"><?php _e('Search Districts:', DAKOII_PDM_TEXT_DOMAIN); ?></label>
                    <input type="search" id="district-search-input" name="s" value="<?php echo esc_attr($search); ?>">
                    <input type="submit" id="search-submit" class="button" value="<?php esc_attr_e('Search Districts', DAKOII_PDM_TEXT_DOMAIN); ?>">
                </p>
            </form>
        </div>

        <?php 
        $current_page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $per_page = 20;
        echo dakoii_pdm()->admin_interface->get_pagination($current_page, $total_items, $per_page, $base_url);
        ?>
    </div>

    <table class="wp-list-table widefat fixed striped">
        <thead>
            <tr>
                <th scope="col" class="manage-column column-name column-primary">
                    <?php _e('Name', DAKOII_PDM_TEXT_DOMAIN); ?>
                </th>
                <th scope="col" class="manage-column column-province">
                    <?php _e('Province', DAKOII_PDM_TEXT_DOMAIN); ?>
                </th>
                <th scope="col" class="manage-column column-slug">
                    <?php _e('Slug', DAKOII_PDM_TEXT_DOMAIN); ?>
                </th>
                <th scope="col" class="manage-column column-content">
                    <?php _e('Content', DAKOII_PDM_TEXT_DOMAIN); ?>
                </th>
                <th scope="col" class="manage-column column-status">
                    <?php _e('Status', DAKOII_PDM_TEXT_DOMAIN); ?>
                </th>
                <th scope="col" class="manage-column column-date">
                    <?php _e('Date', DAKOII_PDM_TEXT_DOMAIN); ?>
                </th>
            </tr>
        </thead>
        <tbody>
            <?php if (empty($districts)): ?>
            <tr>
                <td colspan="6" class="no-items">
                    <?php _e('No districts found.', DAKOII_PDM_TEXT_DOMAIN); ?>
                </td>
            </tr>
            <?php else: ?>
                <?php foreach ($districts as $district): ?>
                <tr>
                    <td class="column-name column-primary">
                        <strong>
                            <a href="<?php echo admin_url('admin.php?page=dakoii-pdm-districts&action=edit&id=' . $district->id); ?>">
                                <?php echo esc_html($district->name); ?>
                            </a>
                        </strong>
                        <?php if (!empty($district->logo_url)): ?>
                        <br><img src="<?php echo esc_url($district->logo_url); ?>" alt="<?php echo esc_attr($district->name); ?>" style="max-width: 40px; max-height: 40px; margin-top: 5px;">
                        <?php endif; ?>
                        <div class="row-actions">
                            <span class="edit">
                                <a href="<?php echo admin_url('admin.php?page=dakoii-pdm-districts&action=edit&id=' . $district->id); ?>">
                                    <?php _e('Edit', DAKOII_PDM_TEXT_DOMAIN); ?>
                                </a> |
                            </span>
                            <span class="delete">
                                <a href="<?php echo wp_nonce_url(admin_url('admin.php?page=dakoii-pdm-districts&action=delete_district&id=' . $district->id), 'dakoii_pdm_delete_district_' . $district->id); ?>" 
                                   class="dakoii-pdm-delete">
                                    <?php _e('Delete', DAKOII_PDM_TEXT_DOMAIN); ?>
                                </a>
                            </span>
                            <?php if (isset($district->content_count) && $district->content_count > 0): ?>
                            | <span class="view">
                                <a href="<?php echo home_url('/district/' . $district->slug); ?>" target="_blank">
                                    <?php _e('View District', DAKOII_PDM_TEXT_DOMAIN); ?>
                                </a>
                            </span>
                            <?php endif; ?>
                        </div>
                        <button type="button" class="toggle-row"><span class="screen-reader-text"><?php _e('Show more details', DAKOII_PDM_TEXT_DOMAIN); ?></span></button>
                    </td>
                    <td class="column-province" data-colname="<?php esc_attr_e('Province', DAKOII_PDM_TEXT_DOMAIN); ?>">
                        <a href="<?php echo admin_url('admin.php?page=dakoii-pdm-districts&province=' . $district->province_id); ?>">
                            <?php echo esc_html($district->province_name); ?>
                        </a>
                    </td>
                    <td class="column-slug" data-colname="<?php esc_attr_e('Slug', DAKOII_PDM_TEXT_DOMAIN); ?>">
                        <code><?php echo esc_html($district->slug); ?></code>
                    </td>
                    <td class="column-content" data-colname="<?php esc_attr_e('Content', DAKOII_PDM_TEXT_DOMAIN); ?>">
                        <?php 
                        if (isset($district->content_count)) {
                            if ($district->content_count > 0) {
                                echo esc_html($district->content_count) . ' ' . __('items', DAKOII_PDM_TEXT_DOMAIN);
                            } else {
                                echo '<span style="color: #999;">' . __('No content', DAKOII_PDM_TEXT_DOMAIN) . '</span>';
                            }
                        }
                        ?>
                    </td>
                    <td class="column-status" data-colname="<?php esc_attr_e('Status', DAKOII_PDM_TEXT_DOMAIN); ?>">
                        <span class="status-<?php echo esc_attr($district->status); ?>">
                            <?php echo $district->status === 'active' ? __('Active', DAKOII_PDM_TEXT_DOMAIN) : __('Inactive', DAKOII_PDM_TEXT_DOMAIN); ?>
                        </span>
                    </td>
                    <td class="column-date" data-colname="<?php esc_attr_e('Date', DAKOII_PDM_TEXT_DOMAIN); ?>">
                        <?php echo esc_html($district->created_at_formatted); ?>
                    </td>
                </tr>
                <?php endforeach; ?>
            <?php endif; ?>
        </tbody>
    </table>

    <div class="tablenav bottom">
        <?php echo dakoii_pdm()->admin_interface->get_pagination($current_page, $total_items, $per_page, $base_url); ?>
    </div>
</div>

<style>
.status-active {
    color: #007cba;
    font-weight: 600;
}
.status-inactive {
    color: #999;
}
.tablenav .alignleft.actions {
    margin-right: 20px;
}
</style>