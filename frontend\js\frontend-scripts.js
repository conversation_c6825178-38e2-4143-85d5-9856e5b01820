/**
 * Dakoii Province District Manager - Frontend JavaScript
 * 
 * @package Dakoii_Province_District_Manager
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */

(function($) {
    'use strict';

    // Plugin namespace
    window.DakoiiPDM = window.DakoiiPDM || {};

    /**
     * Frontend functionality
     */
    DakoiiPDM.Frontend = {
        
        /**
         * Initialize frontend functionality
         */
        init: function() {
            this.bindEvents();
            this.initComponents();
        },

        /**
         * Bind event handlers
         */
        bindEvents: function() {
            // Search functionality
            $(document).on('submit', '.dakoii-search-form', this.handleSearch);
            
            // Load more posts
            $(document).on('click', '.dakoii-load-more', this.loadMorePosts);
            
            // District navigation
            $(document).on('click', '.dakoii-district-nav a', this.handleDistrictNavigation);
        },

        /**
         * Initialize components
         */
        initComponents: function() {
            // Initialize mobile optimizations
            this.initMobileOptimizations();
        },

        /**
         * Handle search form submission
         */
        handleSearch: function(e) {
            e.preventDefault();
            
            var $form = $(this);
            var query = $form.find('.dakoii-search-input').val();
            var districtSlug = $form.data('district');
            
            if (!query.trim()) {
                return false;
            }
            
            DakoiiPDM.Frontend.performSearch(query, districtSlug);
        },

        /**
         * Perform search
         */
        performSearch: function(query, districtSlug) {
            var $resultsContainer = $('.dakoii-search-results');
            
            if (!$resultsContainer.length) {
                $resultsContainer = $('<div class="dakoii-search-results"></div>');
                $('.dakoii-district-main').prepend($resultsContainer);
            }
            
            $resultsContainer.html('<div class="dakoii-loading">Searching...</div>');
            
            $.ajax({
                url: dakoii_pdm_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'dakoii_pdm_search',
                    query: query,
                    district_slug: districtSlug,
                    nonce: dakoii_pdm_ajax.nonce
                },
                success: function(response) {
                    if (response.success) {
                        $resultsContainer.html(response.data.html);
                    } else {
                        $resultsContainer.html('<p>No results found.</p>');
                    }
                }
            });
        },

        /**
         * Initialize mobile optimizations
         */
        initMobileOptimizations: function() {
            // Mobile menu handling
            if ($(window).width() <= 768) {
                $('.dakoii-district-nav').addClass('mobile-responsive');
            }
        }
    };

    /**
     * Initialize when document is ready
     */
    $(document).ready(function() {
        DakoiiPDM.Frontend.init();
    });

})(jQuery);