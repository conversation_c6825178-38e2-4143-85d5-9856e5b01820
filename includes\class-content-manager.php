<?php
/**
 * Content Manager Class
 * 
 * Handles associations between posts/pages and districts
 * 
 * @package Dakoii_Province_District_Manager
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Dakoii_PDM_Content_Manager
{
    /**
     * WordPress database object
     *
     * @var wpdb
     */
    private $wpdb;

    /**
     * Content table name
     *
     * @var string
     */
    private $table_name;

    /**
     * Constructor
     */
    public function __construct()
    {
        global $wpdb;
        $this->wpdb = $wpdb;
        $this->table_name = $wpdb->prefix . 'dakoii_district_content';
        
        $this->init_hooks();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks()
    {
        // Add meta boxes for post/page assignment
        add_action('add_meta_boxes', array($this, 'add_meta_boxes'));
        
        // Save post meta
        add_action('save_post', array($this, 'save_post_meta'), 10, 2);
        
        // Filter main query for district content
        add_action('pre_get_posts', array($this, 'filter_main_query'));
        
        // Add district column to posts list
        add_filter('manage_posts_columns', array($this, 'add_posts_column'));
        add_filter('manage_pages_columns', array($this, 'add_posts_column'));
        add_action('manage_posts_custom_column', array($this, 'display_posts_column'), 10, 2);
        add_action('manage_pages_custom_column', array($this, 'display_posts_column'), 10, 2);
        
        // AJAX handlers
        add_action('wp_ajax_dakoii_pdm_get_district_content', array($this, 'ajax_get_district_content'));
        add_action('wp_ajax_nopriv_dakoii_pdm_get_district_content', array($this, 'ajax_get_district_content'));
    }

    /**
     * Associate content with district
     *
     * @param int $district_id District ID
     * @param int $post_id Post ID
     * @param string $content_type Content type (post, page, menu)
     * @param int $menu_order Menu order
     * @return int|WP_Error Association ID on success, WP_Error on failure
     */
    public function associate_content($district_id, $post_id, $content_type = 'post', $menu_order = 0)
    {
        // Validate inputs
        if (!$this->district_exists($district_id)) {
            return new WP_Error('invalid_district', __('District does not exist.', DAKOII_PDM_TEXT_DOMAIN));
        }

        if (!$this->post_exists($post_id)) {
            return new WP_Error('invalid_post', __('Post does not exist.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Check if association already exists
        if ($this->association_exists($district_id, $post_id)) {
            return new WP_Error('association_exists', __('Content is already associated with this district.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Insert association
        $result = $this->wpdb->insert(
            $this->table_name,
            array(
                'district_id' => $district_id,
                'post_id' => $post_id,
                'content_type' => $content_type,
                'menu_order' => $menu_order
            ),
            array('%d', '%d', '%s', '%d')
        );

        if ($result === false) {
            return new WP_Error('db_error', __('Failed to associate content with district.', DAKOII_PDM_TEXT_DOMAIN));
        }

        $association_id = $this->wpdb->insert_id;

        // Fire action hook
        do_action('dakoii_pdm_content_associated', $association_id, $district_id, $post_id, $content_type);

        return $association_id;
    }

    /**
     * Remove content association
     *
     * @param int $district_id District ID
     * @param int $post_id Post ID
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public function remove_association($district_id, $post_id)
    {
        $result = $this->wpdb->delete(
            $this->table_name,
            array(
                'district_id' => $district_id,
                'post_id' => $post_id
            ),
            array('%d', '%d')
        );

        if ($result === false) {
            return new WP_Error('db_error', __('Failed to remove content association.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Fire action hook
        do_action('dakoii_pdm_content_disassociated', $district_id, $post_id);

        return true;
    }

    /**
     * Get content for a district
     *
     * @param int $district_id District ID
     * @param array $args Query arguments
     * @return array Array of content
     */
    public function get_district_content($district_id, $args = array())
    {
        $defaults = array(
            'content_type' => '', // post, page, menu, or empty for all
            'orderby' => 'menu_order',
            'order' => 'ASC',
            'limit' => 0,
            'offset' => 0,
            'status' => 'publish'
        );

        $args = wp_parse_args($args, $defaults);

        // Build WHERE clause
        $where_conditions = array("dc.district_id = %d");
        $where_values = array($district_id);

        if (!empty($args['content_type'])) {
            $where_conditions[] = "dc.content_type = %s";
            $where_values[] = $args['content_type'];
        }

        if (!empty($args['status'])) {
            $where_conditions[] = "p.post_status = %s";
            $where_values[] = $args['status'];
        }

        $where_clause = 'WHERE ' . implode(' AND ', $where_conditions);

        // Build ORDER BY clause
        $allowed_orderby = array('menu_order', 'post_title', 'post_date', 'post_modified');
        $orderby = in_array($args['orderby'], $allowed_orderby) ? $args['orderby'] : 'menu_order';
        
        if ($orderby === 'menu_order') {
            $orderby = 'dc.menu_order, p.post_title';
        } else {
            $orderby = 'p.' . $orderby;
        }
        
        $order = strtoupper($args['order']) === 'DESC' ? 'DESC' : 'ASC';

        // Build query
        $sql = "SELECT dc.*, p.post_title, p.post_status, p.post_type, p.post_date 
                FROM {$this->table_name} dc 
                INNER JOIN {$this->wpdb->posts} p ON dc.post_id = p.ID 
                {$where_clause} 
                ORDER BY {$orderby} {$order}";

        if ($args['limit'] > 0) {
            $sql .= $this->wpdb->prepare(" LIMIT %d OFFSET %d", $args['limit'], $args['offset']);
        }

        $results = $this->wpdb->get_results(
            $this->wpdb->prepare($sql, $where_values)
        );

        return $results;
    }

    /**
     * Get districts for a post
     *
     * @param int $post_id Post ID
     * @return array Array of districts
     */
    public function get_post_districts($post_id)
    {
        $districts_table = $this->wpdb->prefix . 'dakoii_districts';
        
        $results = $this->wpdb->get_results(
            $this->wpdb->prepare(
                "SELECT d.*, dc.content_type, dc.menu_order 
                 FROM {$this->table_name} dc 
                 INNER JOIN {$districts_table} d ON dc.district_id = d.id 
                 WHERE dc.post_id = %d 
                 ORDER BY d.name ASC",
                $post_id
            )
        );

        return $results;
    }

    /**
     * Update content menu order
     *
     * @param int $district_id District ID
     * @param array $menu_order Array of post_id => order pairs
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public function update_menu_order($district_id, $menu_order)
    {
        foreach ($menu_order as $post_id => $order) {
            $result = $this->wpdb->update(
                $this->table_name,
                array('menu_order' => (int) $order),
                array(
                    'district_id' => $district_id,
                    'post_id' => $post_id
                ),
                array('%d'),
                array('%d', '%d')
            );

            if ($result === false) {
                return new WP_Error('db_error', __('Failed to update menu order.', DAKOII_PDM_TEXT_DOMAIN));
            }
        }

        // Fire action hook
        do_action('dakoii_pdm_menu_order_updated', $district_id, $menu_order);

        return true;
    }

    /**
     * Add meta boxes to post edit screens
     */
    public function add_meta_boxes()
    {
        $post_types = array('post', 'page');
        
        foreach ($post_types as $post_type) {
            add_meta_box(
                'dakoii_pdm_district_assignment',
                __('District Assignment', DAKOII_PDM_TEXT_DOMAIN),
                array($this, 'render_meta_box'),
                $post_type,
                'side',
                'high'
            );
        }
    }

    /**
     * Render district assignment meta box
     *
     * @param WP_Post $post Post object
     */
    public function render_meta_box($post)
    {
        // Add nonce field
        wp_nonce_field('dakoii_pdm_save_meta', 'dakoii_pdm_meta_nonce');

        // Get current districts for this post
        $current_districts = $this->get_post_districts($post->ID);
        $current_district_ids = array_column($current_districts, 'id');

        // Get all active districts
        $districts_table = $this->wpdb->prefix . 'dakoii_districts';
        $provinces_table = $this->wpdb->prefix . 'dakoii_provinces';
        
        $districts = $this->wpdb->get_results(
            "SELECT d.id, d.name, p.name as province_name 
             FROM {$districts_table} d 
             INNER JOIN {$provinces_table} p ON d.province_id = p.id 
             WHERE d.status = 'active' AND p.status = 'active' 
             ORDER BY p.name, d.name"
        );

        ?>
        <div class="dakoii-pdm-district-meta">
            <p><strong><?php _e('Assign to Districts:', DAKOII_PDM_TEXT_DOMAIN); ?></strong></p>
            
            <?php if (empty($districts)): ?>
                <p><?php _e('No active districts available.', DAKOII_PDM_TEXT_DOMAIN); ?></p>
            <?php else: ?>
                <div class="dakoii-district-list">
                    <?php 
                    $current_province = '';
                    foreach ($districts as $district): 
                        if ($current_province !== $district->province_name): 
                            if ($current_province !== '') echo '</div>';
                            $current_province = $district->province_name;
                            echo '<div class="province-group">';
                            echo '<h4>' . esc_html($current_province) . '</h4>';
                        endif;
                    ?>
                        <label>
                            <input type="checkbox" 
                                   name="dakoii_pdm_districts[]" 
                                   value="<?php echo esc_attr($district->id); ?>"
                                   <?php checked(in_array($district->id, $current_district_ids)); ?>>
                            <?php echo esc_html($district->name); ?>
                        </label><br>
                    <?php 
                    endforeach; 
                    if ($current_province !== '') echo '</div>';
                    ?>
                </div>
                
                <p class="description">
                    <?php _e('Select the districts where this content should appear.', DAKOII_PDM_TEXT_DOMAIN); ?>
                </p>
            <?php endif; ?>
        </div>

        <style>
        .dakoii-pdm-district-meta .province-group {
            margin-bottom: 15px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 3px;
        }
        .dakoii-pdm-district-meta .province-group h4 {
            margin: 0 0 8px 0;
            color: #0073aa;
            font-size: 12px;
            text-transform: uppercase;
        }
        .dakoii-pdm-district-meta label {
            display: block;
            margin-bottom: 5px;
            font-size: 12px;
        }
        </style>
        <?php
    }

    /**
     * Save post meta data
     *
     * @param int $post_id Post ID
     * @param WP_Post $post Post object
     */
    public function save_post_meta($post_id, $post)
    {
        // Check if this is an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check nonce
        if (!isset($_POST['dakoii_pdm_meta_nonce']) || 
            !wp_verify_nonce($_POST['dakoii_pdm_meta_nonce'], 'dakoii_pdm_save_meta')) {
            return;
        }

        // Check user permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Only process posts and pages
        if (!in_array($post->post_type, array('post', 'page'))) {
            return;
        }

        // Get selected districts
        $selected_districts = isset($_POST['dakoii_pdm_districts']) ? 
            array_map('intval', $_POST['dakoii_pdm_districts']) : array();

        // Get current associations
        $current_districts = $this->get_post_districts($post_id);
        $current_district_ids = array_column($current_districts, 'id');

        // Determine content type
        $content_type = $post->post_type === 'page' ? 'page' : 'post';

        // Remove old associations not in new selection
        $to_remove = array_diff($current_district_ids, $selected_districts);
        foreach ($to_remove as $district_id) {
            $this->remove_association($district_id, $post_id);
        }

        // Add new associations
        $to_add = array_diff($selected_districts, $current_district_ids);
        foreach ($to_add as $district_id) {
            $this->associate_content($district_id, $post_id, $content_type);
        }
    }

    /**
     * Filter main query for district pages
     *
     * @param WP_Query $query Main query object
     */
    public function filter_main_query($query)
    {
        if (!$query->is_main_query() || is_admin()) {
            return;
        }

        $district_slug = get_query_var('district_slug');
        $district_section = get_query_var('district_section');

        if (!empty($district_slug)) {
            // Get district by slug
            $districts_table = $this->wpdb->prefix . 'dakoii_districts';
            $district = $this->wpdb->get_row(
                $this->wpdb->prepare(
                    "SELECT * FROM {$districts_table} WHERE slug = %s AND status = 'active'",
                    $district_slug
                )
            );

            if ($district) {
                // Get district content
                $content_args = array();
                
                if ($district_section === 'posts') {
                    $content_args['content_type'] = 'post';
                } elseif ($district_section === 'pages') {
                    $content_args['content_type'] = 'page';
                }

                $district_content = $this->get_district_content($district->id, $content_args);
                $post_ids = array_column($district_content, 'post_id');

                if (!empty($post_ids)) {
                    $query->set('post__in', $post_ids);
                    $query->set('orderby', 'post__in');
                } else {
                    // No content found, show empty result
                    $query->set('post__in', array(0));
                }
            } else {
                // District not found, show 404
                $query->set_404();
            }
        }
    }

    /**
     * Add district column to posts list
     *
     * @param array $columns Existing columns
     * @return array Modified columns
     */
    public function add_posts_column($columns)
    {
        $columns['dakoii_districts'] = __('Districts', DAKOII_PDM_TEXT_DOMAIN);
        return $columns;
    }

    /**
     * Display district column content
     *
     * @param string $column Column name
     * @param int $post_id Post ID
     */
    public function display_posts_column($column, $post_id)
    {
        if ($column === 'dakoii_districts') {
            $districts = $this->get_post_districts($post_id);
            
            if (!empty($districts)) {
                $district_names = array_column($districts, 'name');
                echo esc_html(implode(', ', $district_names));
            } else {
                echo '<span style="color: #999;">' . __('Not assigned', DAKOII_PDM_TEXT_DOMAIN) . '</span>';
            }
        }
    }

    /**
     * AJAX handler for getting district content
     */
    public function ajax_get_district_content()
    {
        // Check nonce
        if (!wp_verify_nonce($_POST['nonce'], 'dakoii_pdm_nonce')) {
            wp_die('Invalid nonce');
        }

        $district_id = intval($_POST['district_id']);
        $content_type = sanitize_text_field($_POST['content_type']);

        $content = $this->get_district_content($district_id, array(
            'content_type' => $content_type
        ));

        wp_send_json_success($content);
    }

    /**
     * Check if district exists
     *
     * @param int $district_id District ID
     * @return bool True if exists, false otherwise
     */
    private function district_exists($district_id)
    {
        $districts_table = $this->wpdb->prefix . 'dakoii_districts';
        
        $count = $this->wpdb->get_var(
            $this->wpdb->prepare(
                "SELECT COUNT(*) FROM {$districts_table} WHERE id = %d",
                $district_id
            )
        );

        return $count > 0;
    }

    /**
     * Check if post exists
     *
     * @param int $post_id Post ID
     * @return bool True if exists, false otherwise
     */
    private function post_exists($post_id)
    {
        $post = get_post($post_id);
        return $post !== null;
    }

    /**
     * Check if association exists
     *
     * @param int $district_id District ID
     * @param int $post_id Post ID
     * @return bool True if exists, false otherwise
     */
    private function association_exists($district_id, $post_id)
    {
        $count = $this->wpdb->get_var(
            $this->wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->table_name} WHERE district_id = %d AND post_id = %d",
                $district_id,
                $post_id
            )
        );

        return $count > 0;
    }

    /**
     * Get content statistics
     *
     * @return array Statistics array
     */
    public function get_statistics()
    {
        $stats = array();

        // Total associations
        $stats['total_associations'] = $this->wpdb->get_var(
            "SELECT COUNT(*) FROM {$this->table_name}"
        );

        // Associations by content type
        $stats['by_content_type'] = $this->wpdb->get_results(
            "SELECT content_type, COUNT(*) as count 
             FROM {$this->table_name} 
             GROUP BY content_type",
            ARRAY_A
        );

        // Top districts by content count
        $stats['top_districts'] = $this->wpdb->get_results(
            "SELECT d.name as district_name, COUNT(dc.id) as content_count 
             FROM {$this->table_name} dc 
             INNER JOIN {$this->wpdb->prefix}dakoii_districts d ON dc.district_id = d.id 
             GROUP BY dc.district_id, d.name 
             ORDER BY content_count DESC 
             LIMIT 10",
            ARRAY_A
        );

        return $stats;
    }
}