<?php
/**
 * Admin Interface Class
 * 
 * Handles all administrative interface functionality
 * 
 * @package Dakoii_Province_District_Manager
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Dakoii_PDM_Admin_Interface
{
    /**
     * Province manager instance
     *
     * @var Dakoii_PDM_Province_Manager
     */
    private $province_manager;

    /**
     * District manager instance
     *
     * @var Dakoii_PDM_District_Manager
     */
    private $district_manager;

    /**
     * Content manager instance
     *
     * @var Dakoii_PDM_Content_Manager
     */
    private $content_manager;

    /**
     * Constructor
     */
    public function __construct()
    {
        $this->province_manager = new Dakoii_PDM_Province_Manager();
        $this->district_manager = new Dakoii_PDM_District_Manager();
        $this->content_manager = new Dakoii_PDM_Content_Manager();
        
        $this->init_hooks();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks()
    {
        add_action('admin_menu', array($this, 'add_admin_menu'));
        add_action('admin_init', array($this, 'handle_admin_actions'));
        add_action('wp_ajax_dakoii_pdm_get_districts', array($this, 'ajax_get_districts'));
    }

    /**
     * Add admin menu items
     */
    public function add_admin_menu()
    {
        // Main menu
        add_menu_page(
            __('Dakoii Manager', DAKOII_PDM_TEXT_DOMAIN),
            __('Dakoii Manager', DAKOII_PDM_TEXT_DOMAIN),
            'manage_options',
            'dakoii-pdm-dashboard',
            array($this, 'render_dashboard'),
            'dashicons-location-alt',
            30
        );

        // Dashboard submenu
        add_submenu_page(
            'dakoii-pdm-dashboard',
            __('Dashboard', DAKOII_PDM_TEXT_DOMAIN),
            __('Dashboard', DAKOII_PDM_TEXT_DOMAIN),
            'manage_options',
            'dakoii-pdm-dashboard',
            array($this, 'render_dashboard')
        );

        // Provinces submenu
        add_submenu_page(
            'dakoii-pdm-dashboard',
            __('Provinces', DAKOII_PDM_TEXT_DOMAIN),
            __('Provinces', DAKOII_PDM_TEXT_DOMAIN),
            'manage_options',
            'dakoii-pdm-provinces',
            array($this, 'render_provinces')
        );

        // Add Province submenu
        add_submenu_page(
            'dakoii-pdm-dashboard',
            __('Add Province', DAKOII_PDM_TEXT_DOMAIN),
            __('Add Province', DAKOII_PDM_TEXT_DOMAIN),
            'manage_options',
            'dakoii-pdm-add-province',
            array($this, 'render_add_province')
        );

        // Districts submenu
        add_submenu_page(
            'dakoii-pdm-dashboard',
            __('Districts', DAKOII_PDM_TEXT_DOMAIN),
            __('Districts', DAKOII_PDM_TEXT_DOMAIN),
            'manage_options',
            'dakoii-pdm-districts',
            array($this, 'render_districts')
        );

        // Add District submenu
        add_submenu_page(
            'dakoii-pdm-dashboard',
            __('Add District', DAKOII_PDM_TEXT_DOMAIN),
            __('Add District', DAKOII_PDM_TEXT_DOMAIN),
            'manage_options',
            'dakoii-pdm-add-district',
            array($this, 'render_add_district')
        );
    }

    /**
     * Handle admin actions
     */
    public function handle_admin_actions()
    {
        if (!current_user_can('manage_options')) {
            return;
        }

        $action = isset($_REQUEST['action']) ? $_REQUEST['action'] : '';

        switch ($action) {
            case 'add_province':
                $this->handle_add_province();
                break;
            case 'edit_province':
                $this->handle_edit_province();
                break;
            case 'delete_province':
                $this->handle_delete_province();
                break;
            case 'add_district':
                $this->handle_add_district();
                break;
            case 'edit_district':
                $this->handle_edit_district();
                break;
            case 'delete_district':
                $this->handle_delete_district();
                break;
        }
    }

    /**
     * Render dashboard page
     */
    public function render_dashboard()
    {
        // Get statistics
        $province_stats = $this->province_manager->get_statistics();
        $district_stats = $this->district_manager->get_statistics();
        $content_stats = $this->content_manager->get_statistics();

        include DAKOII_PDM_PLUGIN_DIR . 'admin/views/dashboard.php';
    }

    /**
     * Render provinces page
     */
    public function render_provinces()
    {
        $action = isset($_GET['action']) ? $_GET['action'] : 'list';
        $province_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

        if ($action === 'edit' && $province_id > 0) {
            $province = $this->province_manager->read($province_id);
            if ($province) {
                include DAKOII_PDM_PLUGIN_DIR . 'admin/views/provinces-form.php';
                return;
            }
        }

        // Default to list view
        $page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $per_page = 20;
        $search = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';

        $args = array(
            'search' => $search,
            'limit' => $per_page,
            'offset' => ($page - 1) * $per_page
        );

        $results = $this->province_manager->get_all($args);
        $provinces = $results['provinces'];
        $total_items = $results['total_count'];

        include DAKOII_PDM_PLUGIN_DIR . 'admin/views/provinces-list.php';
    }

    /**
     * Render add province page
     */
    public function render_add_province()
    {
        $province = null; // New province
        include DAKOII_PDM_PLUGIN_DIR . 'admin/views/provinces-form.php';
    }

    /**
     * Render districts page
     */
    public function render_districts()
    {
        $action = isset($_GET['action']) ? $_GET['action'] : 'list';
        $district_id = isset($_GET['id']) ? intval($_GET['id']) : 0;

        if ($action === 'edit' && $district_id > 0) {
            $district = $this->district_manager->read($district_id);
            if ($district) {
                $provinces = $this->province_manager->get_options();
                include DAKOII_PDM_PLUGIN_DIR . 'admin/views/districts-form.php';
                return;
            }
        }

        // Default to list view
        $page = isset($_GET['paged']) ? max(1, intval($_GET['paged'])) : 1;
        $per_page = 20;
        $search = isset($_GET['s']) ? sanitize_text_field($_GET['s']) : '';
        $province_filter = isset($_GET['province']) ? intval($_GET['province']) : 0;

        $args = array(
            'search' => $search,
            'province_id' => $province_filter,
            'limit' => $per_page,
            'offset' => ($page - 1) * $per_page
        );

        $results = $this->district_manager->get_all($args);
        $districts = $results['districts'];
        $total_items = $results['total_count'];
        $provinces = $this->province_manager->get_options();

        include DAKOII_PDM_PLUGIN_DIR . 'admin/views/districts-list.php';
    }

    /**
     * Render add district page
     */
    public function render_add_district()
    {
        $district = null; // New district
        $provinces = $this->province_manager->get_options();
        include DAKOII_PDM_PLUGIN_DIR . 'admin/views/districts-form.php';
    }

    /**
     * Handle add province action
     */
    private function handle_add_province()
    {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'dakoii_pdm_add_province')) {
            wp_die(__('Security check failed.', DAKOII_PDM_TEXT_DOMAIN));
        }

        $data = array(
            'name' => sanitize_text_field($_POST['name']),
            'slug' => sanitize_title($_POST['slug']),
            'description' => sanitize_textarea_field($_POST['description']),
            'status' => sanitize_text_field($_POST['status'])
        );

        $result = $this->province_manager->create($data);

        if (is_wp_error($result)) {
            $this->add_admin_notice($result->get_error_message(), 'error');
        } else {
            $this->add_admin_notice(__('Province created successfully.', DAKOII_PDM_TEXT_DOMAIN), 'success');
            wp_redirect(admin_url('admin.php?page=dakoii-pdm-provinces'));
            exit;
        }
    }

    /**
     * Handle edit province action
     */
    private function handle_edit_province()
    {
        $province_id = intval($_POST['province_id']);

        if (!wp_verify_nonce($_POST['_wpnonce'], 'dakoii_pdm_edit_province_' . $province_id)) {
            wp_die(__('Security check failed.', DAKOII_PDM_TEXT_DOMAIN));
        }

        $data = array(
            'name' => sanitize_text_field($_POST['name']),
            'slug' => sanitize_title($_POST['slug']),
            'description' => sanitize_textarea_field($_POST['description']),
            'status' => sanitize_text_field($_POST['status'])
        );

        $result = $this->province_manager->update($province_id, $data);

        if (is_wp_error($result)) {
            $this->add_admin_notice($result->get_error_message(), 'error');
        } else {
            $this->add_admin_notice(__('Province updated successfully.', DAKOII_PDM_TEXT_DOMAIN), 'success');
            wp_redirect(admin_url('admin.php?page=dakoii-pdm-provinces'));
            exit;
        }
    }

    /**
     * Handle delete province action
     */
    private function handle_delete_province()
    {
        $province_id = intval($_GET['id']);

        if (!wp_verify_nonce($_GET['_wpnonce'], 'dakoii_pdm_delete_province_' . $province_id)) {
            wp_die(__('Security check failed.', DAKOII_PDM_TEXT_DOMAIN));
        }

        $force_delete = isset($_GET['force']) && $_GET['force'] === '1';
        $result = $this->province_manager->delete($province_id, $force_delete);

        if (is_wp_error($result)) {
            $this->add_admin_notice($result->get_error_message(), 'error');
        } else {
            $this->add_admin_notice(__('Province deleted successfully.', DAKOII_PDM_TEXT_DOMAIN), 'success');
        }

        wp_redirect(admin_url('admin.php?page=dakoii-pdm-provinces'));
        exit;
    }

    /**
     * Handle add district action
     */
    private function handle_add_district()
    {
        if (!wp_verify_nonce($_POST['_wpnonce'], 'dakoii_pdm_add_district')) {
            wp_die(__('Security check failed.', DAKOII_PDM_TEXT_DOMAIN));
        }

        $data = array(
            'province_id' => intval($_POST['province_id']),
            'name' => sanitize_text_field($_POST['name']),
            'slug' => sanitize_title($_POST['slug']),
            'description' => sanitize_textarea_field($_POST['description']),
            'logo_url' => esc_url_raw($_POST['logo_url']),
            'status' => sanitize_text_field($_POST['status'])
        );

        // Handle contact info
        if (isset($_POST['contact_info'])) {
            $contact_info = array();
            foreach ($_POST['contact_info'] as $key => $value) {
                $contact_info[sanitize_key($key)] = sanitize_text_field($value);
            }
            $data['contact_info'] = $contact_info;
        }

        $result = $this->district_manager->create($data);

        if (is_wp_error($result)) {
            $this->add_admin_notice($result->get_error_message(), 'error');
        } else {
            $this->add_admin_notice(__('District created successfully.', DAKOII_PDM_TEXT_DOMAIN), 'success');
            wp_redirect(admin_url('admin.php?page=dakoii-pdm-districts'));
            exit;
        }
    }

    /**
     * Handle edit district action
     */
    private function handle_edit_district()
    {
        $district_id = intval($_POST['district_id']);

        if (!wp_verify_nonce($_POST['_wpnonce'], 'dakoii_pdm_edit_district_' . $district_id)) {
            wp_die(__('Security check failed.', DAKOII_PDM_TEXT_DOMAIN));
        }

        $data = array(
            'province_id' => intval($_POST['province_id']),
            'name' => sanitize_text_field($_POST['name']),
            'slug' => sanitize_title($_POST['slug']),
            'description' => sanitize_textarea_field($_POST['description']),
            'logo_url' => esc_url_raw($_POST['logo_url']),
            'status' => sanitize_text_field($_POST['status'])
        );

        // Handle contact info
        if (isset($_POST['contact_info'])) {
            $contact_info = array();
            foreach ($_POST['contact_info'] as $key => $value) {
                $contact_info[sanitize_key($key)] = sanitize_text_field($value);
            }
            $data['contact_info'] = $contact_info;
        }

        $result = $this->district_manager->update($district_id, $data);

        if (is_wp_error($result)) {
            $this->add_admin_notice($result->get_error_message(), 'error');
        } else {
            $this->add_admin_notice(__('District updated successfully.', DAKOII_PDM_TEXT_DOMAIN), 'success');
            wp_redirect(admin_url('admin.php?page=dakoii-pdm-districts'));
            exit;
        }
    }

    /**
     * Handle delete district action
     */
    private function handle_delete_district()
    {
        $district_id = intval($_GET['id']);

        if (!wp_verify_nonce($_GET['_wpnonce'], 'dakoii_pdm_delete_district_' . $district_id)) {
            wp_die(__('Security check failed.', DAKOII_PDM_TEXT_DOMAIN));
        }

        $force_delete = isset($_GET['force']) && $_GET['force'] === '1';
        $result = $this->district_manager->delete($district_id, $force_delete);

        if (is_wp_error($result)) {
            $this->add_admin_notice($result->get_error_message(), 'error');
        } else {
            $this->add_admin_notice(__('District deleted successfully.', DAKOII_PDM_TEXT_DOMAIN), 'success');
        }

        wp_redirect(admin_url('admin.php?page=dakoii-pdm-districts'));
        exit;
    }

    /**
     * AJAX handler for getting districts by province
     */
    public function ajax_get_districts()
    {
        if (!wp_verify_nonce($_POST['nonce'], 'dakoii_pdm_admin_nonce')) {
            wp_die('Invalid nonce');
        }

        $province_id = intval($_POST['province_id']);
        $districts = $this->district_manager->get_by_province($province_id);

        wp_send_json_success($districts);
    }

    /**
     * Add admin notice
     *
     * @param string $message Notice message
     * @param string $type Notice type (success, error, warning, info)
     */
    private function add_admin_notice($message, $type = 'info')
    {
        add_action('admin_notices', function() use ($message, $type) {
            $class = 'notice notice-' . $type;
            printf('<div class="%1$s"><p>%2$s</p></div>', esc_attr($class), esc_html($message));
        });
    }

    /**
     * Generate pagination links
     *
     * @param int $current_page Current page number
     * @param int $total_items Total number of items
     * @param int $per_page Items per page
     * @param string $base_url Base URL for pagination
     * @return string Pagination HTML
     */
    public function get_pagination($current_page, $total_items, $per_page, $base_url)
    {
        $total_pages = ceil($total_items / $per_page);

        if ($total_pages <= 1) {
            return '';
        }

        $pagination = '<div class="tablenav-pages">';
        $pagination .= '<span class="displaying-num">' . 
                      sprintf(_n('%d item', '%d items', $total_items, DAKOII_PDM_TEXT_DOMAIN), $total_items) . 
                      '</span>';

        $pagination .= '<span class="pagination-links">';

        // First page
        if ($current_page > 1) {
            $pagination .= '<a class="first-page button" href="' . esc_url($base_url . '&paged=1') . '">&laquo;</a>';
            $pagination .= '<a class="prev-page button" href="' . esc_url($base_url . '&paged=' . ($current_page - 1)) . '">&lsaquo;</a>';
        } else {
            $pagination .= '<span class="tablenav-pages-navspan button disabled">&laquo;</span>';
            $pagination .= '<span class="tablenav-pages-navspan button disabled">&lsaquo;</span>';
        }

        // Current page
        $pagination .= '<span class="paging-input">';
        $pagination .= '<input class="current-page" type="text" name="paged" value="' . $current_page . '" size="2" readonly>';
        $pagination .= ' of <span class="total-pages">' . $total_pages . '</span>';
        $pagination .= '</span>';

        // Last page
        if ($current_page < $total_pages) {
            $pagination .= '<a class="next-page button" href="' . esc_url($base_url . '&paged=' . ($current_page + 1)) . '">&rsaquo;</a>';
            $pagination .= '<a class="last-page button" href="' . esc_url($base_url . '&paged=' . $total_pages) . '">&raquo;</a>';
        } else {
            $pagination .= '<span class="tablenav-pages-navspan button disabled">&rsaquo;</span>';
            $pagination .= '<span class="tablenav-pages-navspan button disabled">&raquo;</span>';
        }

        $pagination .= '</span>';
        $pagination .= '</div>';

        return $pagination;
    }
}