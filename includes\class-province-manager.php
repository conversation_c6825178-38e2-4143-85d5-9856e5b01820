<?php
/**
 * Province Manager Class
 * 
 * Handles all CRUD operations for provinces
 * 
 * @package Dakoii_Province_District_Manager
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Dakoii_PDM_Province_Manager
{
    /**
     * WordPress database object
     *
     * @var wpdb
     */
    private $wpdb;

    /**
     * Provinces table name
     *
     * @var string
     */
    private $table_name;

    /**
     * Constructor
     */
    public function __construct()
    {
        global $wpdb;
        $this->wpdb = $wpdb;
        $this->table_name = $wpdb->prefix . 'dakoii_provinces';
    }

    /**
     * Create a new province
     *
     * @param array $data Province data
     * @return int|WP_Error Province ID on success, WP_Error on failure
     */
    public function create($data)
    {
        // Validate required fields
        $validation = $this->validate_data($data);
        if (is_wp_error($validation)) {
            return $validation;
        }

        // Prepare data for insertion
        $province_data = $this->prepare_data($data);
        
        // Check if slug already exists
        if ($this->slug_exists($province_data['slug'])) {
            return new WP_Error('duplicate_slug', __('Province slug already exists.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Insert into database
        $result = $this->wpdb->insert(
            $this->table_name,
            $province_data,
            array('%s', '%s', '%s', '%s', '%s', '%s')
        );

        if ($result === false) {
            return new WP_Error('db_error', __('Failed to create province.', DAKOII_PDM_TEXT_DOMAIN));
        }

        $province_id = $this->wpdb->insert_id;

        // Fire action hook
        do_action('dakoii_pdm_province_created', $province_id, $province_data);

        return $province_id;
    }

    /**
     * Read a province by ID
     *
     * @param int $id Province ID
     * @return object|null Province object or null if not found
     */
    public function read($id)
    {
        $province = $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->table_name} WHERE id = %d",
                $id
            )
        );

        if ($province) {
            $province = $this->format_province_data($province);
        }

        return $province;
    }

    /**
     * Read a province by slug
     *
     * @param string $slug Province slug
     * @return object|null Province object or null if not found
     */
    public function read_by_slug($slug)
    {
        $province = $this->wpdb->get_row(
            $this->wpdb->prepare(
                "SELECT * FROM {$this->table_name} WHERE slug = %s",
                $slug
            )
        );

        if ($province) {
            $province = $this->format_province_data($province);
        }

        return $province;
    }

    /**
     * Update a province
     *
     * @param int $id Province ID
     * @param array $data Updated province data
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public function update($id, $data)
    {
        // Check if province exists
        $existing_province = $this->read($id);
        if (!$existing_province) {
            return new WP_Error('not_found', __('Province not found.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Validate data
        $validation = $this->validate_data($data, $id);
        if (is_wp_error($validation)) {
            return $validation;
        }

        // Prepare data for update
        $province_data = $this->prepare_data($data, true);

        // Check if slug already exists (excluding current province)
        if (isset($province_data['slug']) && $this->slug_exists($province_data['slug'], $id)) {
            return new WP_Error('duplicate_slug', __('Province slug already exists.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Update database
        $result = $this->wpdb->update(
            $this->table_name,
            $province_data,
            array('id' => $id),
            array('%s', '%s', '%s', '%s'),
            array('%d')
        );

        if ($result === false) {
            return new WP_Error('db_error', __('Failed to update province.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Fire action hook
        do_action('dakoii_pdm_province_updated', $id, $province_data, $existing_province);

        return true;
    }

    /**
     * Delete a province
     *
     * @param int $id Province ID
     * @param bool $force_delete Whether to force delete even with districts
     * @return bool|WP_Error True on success, WP_Error on failure
     */
    public function delete($id, $force_delete = false)
    {
        // Check if province exists
        $province = $this->read($id);
        if (!$province) {
            return new WP_Error('not_found', __('Province not found.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Check for associated districts
        if (!$force_delete) {
            $district_count = $this->get_district_count($id);
            if ($district_count > 0) {
                return new WP_Error(
                    'has_districts', 
                    sprintf(
                        __('Cannot delete province. It has %d associated district(s).', DAKOII_PDM_TEXT_DOMAIN),
                        $district_count
                    )
                );
            }
        }

        // Delete from database
        $result = $this->wpdb->delete(
            $this->table_name,
            array('id' => $id),
            array('%d')
        );

        if ($result === false) {
            return new WP_Error('db_error', __('Failed to delete province.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Fire action hook
        do_action('dakoii_pdm_province_deleted', $id, $province);

        return true;
    }

    /**
     * Get all provinces with pagination
     *
     * @param array $args Query arguments
     * @return array Array with provinces and pagination info
     */
    public function get_all($args = array())
    {
        $defaults = array(
            'status' => 'active',
            'search' => '',
            'orderby' => 'name',
            'order' => 'ASC',
            'limit' => 20,
            'offset' => 0,
            'include_counts' => true
        );

        $args = wp_parse_args($args, $defaults);

        // Build WHERE clause
        $where_conditions = array();
        $where_values = array();

        if (!empty($args['status'])) {
            $where_conditions[] = "status = %s";
            $where_values[] = $args['status'];
        }

        if (!empty($args['search'])) {
            $where_conditions[] = "(name LIKE %s OR description LIKE %s)";
            $search_term = '%' . $this->wpdb->esc_like($args['search']) . '%';
            $where_values[] = $search_term;
            $where_values[] = $search_term;
        }

        $where_clause = !empty($where_conditions) ? 'WHERE ' . implode(' AND ', $where_conditions) : '';

        // Build ORDER BY clause
        $allowed_orderby = array('id', 'name', 'slug', 'status', 'created_at', 'updated_at');
        $orderby = in_array($args['orderby'], $allowed_orderby) ? $args['orderby'] : 'name';
        $order = strtoupper($args['order']) === 'DESC' ? 'DESC' : 'ASC';

        // Get total count
        $count_sql = "SELECT COUNT(*) FROM {$this->table_name} {$where_clause}";
        if (!empty($where_values)) {
            $count_sql = $this->wpdb->prepare($count_sql, $where_values);
        }
        $total_count = $this->wpdb->get_var($count_sql);

        // Get provinces
        $sql = "SELECT * FROM {$this->table_name} {$where_clause} ORDER BY {$orderby} {$order}";
        
        if ($args['limit'] > 0) {
            $sql .= $this->wpdb->prepare(" LIMIT %d OFFSET %d", $args['limit'], $args['offset']);
        }

        if (!empty($where_values)) {
            $sql = $this->wpdb->prepare($sql, $where_values);
        }

        $provinces = $this->wpdb->get_results($sql);

        // Format province data
        foreach ($provinces as &$province) {
            $province = $this->format_province_data($province);
            
            if ($args['include_counts']) {
                $province->district_count = $this->get_district_count($province->id);
            }
        }

        return array(
            'provinces' => $provinces,
            'total_count' => $total_count,
            'found_rows' => count($provinces)
        );
    }

    /**
     * Get province options for select fields
     *
     * @param string $status Province status
     * @return array Array of province options
     */
    public function get_options($status = 'active')
    {
        $provinces = $this->wpdb->get_results(
            $this->wpdb->prepare(
                "SELECT id, name FROM {$this->table_name} WHERE status = %s ORDER BY name ASC",
                $status
            )
        );

        $options = array();
        foreach ($provinces as $province) {
            $options[$province->id] = $province->name;
        }

        return $options;
    }

    /**
     * Check if slug exists
     *
     * @param string $slug Province slug
     * @param int $exclude_id ID to exclude from check
     * @return bool True if slug exists, false otherwise
     */
    private function slug_exists($slug, $exclude_id = 0)
    {
        $sql = "SELECT COUNT(*) FROM {$this->table_name} WHERE slug = %s";
        $values = array($slug);

        if ($exclude_id > 0) {
            $sql .= " AND id != %d";
            $values[] = $exclude_id;
        }

        $count = $this->wpdb->get_var($this->wpdb->prepare($sql, $values));
        
        return $count > 0;
    }

    /**
     * Get district count for a province
     *
     * @param int $province_id Province ID
     * @return int District count
     */
    private function get_district_count($province_id)
    {
        $districts_table = $this->wpdb->prefix . 'dakoii_districts';
        
        return $this->wpdb->get_var(
            $this->wpdb->prepare(
                "SELECT COUNT(*) FROM {$districts_table} WHERE province_id = %d",
                $province_id
            )
        );
    }

    /**
     * Validate province data
     *
     * @param array $data Province data
     * @param int $id Province ID (for updates)
     * @return bool|WP_Error True if valid, WP_Error on validation failure
     */
    private function validate_data($data, $id = 0)
    {
        $errors = new WP_Error();

        // Validate required fields
        if (empty($data['name'])) {
            $errors->add('missing_name', __('Province name is required.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Validate name length
        if (isset($data['name']) && strlen($data['name']) > 255) {
            $errors->add('name_too_long', __('Province name must be 255 characters or less.', DAKOII_PDM_TEXT_DOMAIN));
        }

        // Validate slug
        if (isset($data['slug'])) {
            if (strlen($data['slug']) > 255) {
                $errors->add('slug_too_long', __('Province slug must be 255 characters or less.', DAKOII_PDM_TEXT_DOMAIN));
            }
            
            if (!preg_match('/^[a-z0-9-]+$/', $data['slug'])) {
                $errors->add('invalid_slug', __('Province slug can only contain lowercase letters, numbers, and hyphens.', DAKOII_PDM_TEXT_DOMAIN));
            }
        }

        // Validate status
        if (isset($data['status']) && !in_array($data['status'], array('active', 'inactive'))) {
            $errors->add('invalid_status', __('Invalid province status.', DAKOII_PDM_TEXT_DOMAIN));
        }

        return $errors->get_error_codes() ? $errors : true;
    }

    /**
     * Prepare province data for database insertion/update
     *
     * @param array $data Raw province data
     * @param bool $is_update Whether this is an update operation
     * @return array Prepared data
     */
    private function prepare_data($data, $is_update = false)
    {
        $prepared = array();

        // Required fields
        if (isset($data['name'])) {
            $prepared['name'] = sanitize_text_field($data['name']);
        }

        // Generate slug if not provided
        if (isset($data['slug'])) {
            $prepared['slug'] = sanitize_title($data['slug']);
        } elseif (isset($data['name']) && !$is_update) {
            $prepared['slug'] = sanitize_title($data['name']);
        }

        // Optional fields
        if (isset($data['description'])) {
            $prepared['description'] = sanitize_textarea_field($data['description']);
        }

        if (isset($data['status'])) {
            $prepared['status'] = sanitize_text_field($data['status']);
        } elseif (!$is_update) {
            $prepared['status'] = 'active';
        }

        // Timestamps are handled by database

        return $prepared;
    }

    /**
     * Format province data for output
     *
     * @param object $province Raw province data from database
     * @return object Formatted province data
     */
    private function format_province_data($province)
    {
        // Ensure all expected properties exist
        $province->id = (int) $province->id;
        $province->name = $province->name ?: '';
        $province->slug = $province->slug ?: '';
        $province->description = $province->description ?: '';
        $province->status = $province->status ?: 'active';
        
        // Format dates
        if (!empty($province->created_at)) {
            $province->created_at_formatted = date_i18n(get_option('date_format'), strtotime($province->created_at));
        }
        
        if (!empty($province->updated_at)) {
            $province->updated_at_formatted = date_i18n(get_option('date_format'), strtotime($province->updated_at));
        }

        return $province;
    }

    /**
     * Bulk delete provinces
     *
     * @param array $ids Province IDs
     * @param bool $force_delete Whether to force delete even with districts
     * @return array Results with success/error counts
     */
    public function bulk_delete($ids, $force_delete = false)
    {
        $results = array(
            'success' => 0,
            'errors' => 0,
            'messages' => array()
        );

        foreach ($ids as $id) {
            $result = $this->delete($id, $force_delete);
            
            if (is_wp_error($result)) {
                $results['errors']++;
                $results['messages'][] = sprintf(
                    __('Failed to delete province ID %d: %s', DAKOII_PDM_TEXT_DOMAIN),
                    $id,
                    $result->get_error_message()
                );
            } else {
                $results['success']++;
            }
        }

        return $results;
    }

    /**
     * Get province statistics
     *
     * @return array Statistics array
     */
    public function get_statistics()
    {
        $stats = array();

        // Total provinces
        $stats['total'] = $this->wpdb->get_var("SELECT COUNT(*) FROM {$this->table_name}");
        
        // Active provinces
        $stats['active'] = $this->wpdb->get_var(
            $this->wpdb->prepare(
                "SELECT COUNT(*) FROM {$this->table_name} WHERE status = %s",
                'active'
            )
        );
        
        // Inactive provinces
        $stats['inactive'] = $stats['total'] - $stats['active'];

        return $stats;
    }
}