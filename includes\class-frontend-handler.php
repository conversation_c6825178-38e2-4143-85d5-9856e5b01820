<?php
/**
 * Frontend Handler Class
 * 
 * Handles frontend display and functionality
 * 
 * @package Dakoii_Province_District_Manager
 * <AUTHOR> <<EMAIL>>
 * @since 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

class Dakoii_PDM_Frontend_Handler
{
    /**
     * Constructor
     */
    public function __construct()
    {
        $this->init_hooks();
    }

    /**
     * Initialize WordPress hooks
     */
    private function init_hooks()
    {
        add_action('template_redirect', array($this, 'handle_district_pages'));
        add_filter('the_content', array($this, 'modify_district_content'));
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
        
        // AJAX handlers
        add_action('wp_ajax_dakoii_pdm_search', array($this, 'ajax_search_content'));
        add_action('wp_ajax_nopriv_dakoii_pdm_search', array($this, 'ajax_search_content'));
    }

    /**
     * Handle district page requests
     */
    public function handle_district_pages()
    {
        $district_slug = get_query_var('district_slug');
        
        if (empty($district_slug)) {
            return;
        }

        // Get district
        $district_manager = new Dakoii_PDM_District_Manager();
        $district = $district_manager->read_by_slug($district_slug);
        
        if (!$district) {
            global $wp_query;
            $wp_query->set_404();
            status_header(404);
            get_template_part(404);
            exit;
        }

        // Set global district data
        global $dakoii_current_district;
        $dakoii_current_district = $district;

        // Load template
        $this->load_district_template($district);
    }

    /**
     * Load district template
     */
    private function load_district_template($district)
    {
        get_header();
        
        echo '<div class="dakoii-district-wrapper">';
        echo '<div class="dakoii-district-header">';
        
        if (!empty($district->logo_url)) {
            echo '<img src="' . esc_url($district->logo_url) . '" alt="' . esc_attr($district->name) . '" class="dakoii-district-logo">';
        }
        
        echo '<h1 class="dakoii-district-title">' . esc_html($district->name) . '</h1>';
        
        if (!empty($district->description)) {
            echo '<div class="dakoii-district-description">' . wp_kses_post(wpautop($district->description)) . '</div>';
        }
        
        echo '</div>';

        // Get and display content
        $content_manager = new Dakoii_PDM_Content_Manager();
        $district_section = get_query_var('district_section');
        
        if ($district_section === 'posts') {
            $content = $content_manager->get_district_content($district->id, array('content_type' => 'post'));
            echo '<h2>' . __('Posts', DAKOII_PDM_TEXT_DOMAIN) . '</h2>';
        } elseif ($district_section === 'pages') {
            $content = $content_manager->get_district_content($district->id, array('content_type' => 'page'));
            echo '<h2>' . __('Pages', DAKOII_PDM_TEXT_DOMAIN) . '</h2>';
        } else {
            $content = $content_manager->get_district_content($district->id, array('limit' => 5));
            echo '<h2>' . __('Recent Content', DAKOII_PDM_TEXT_DOMAIN) . '</h2>';
        }

        if (!empty($content)) {
            echo '<div class="dakoii-district-content">';
            foreach ($content as $item) {
                $post = get_post($item->post_id);
                echo '<div class="dakoii-content-item">';
                echo '<h3><a href="' . get_permalink($post) . '">' . esc_html($post->post_title) . '</a></h3>';
                echo '<p>' . wp_trim_words($post->post_content, 20) . '</p>';
                echo '</div>';
            }
            echo '</div>';
        } else {
            echo '<p>' . __('No content available for this district.', DAKOII_PDM_TEXT_DOMAIN) . '</p>';
        }

        echo '</div>';
        get_footer();
        exit;
    }

    /**
     * Modify content for district context
     */
    public function modify_district_content($content)
    {
        global $dakoii_current_district;
        
        if (!empty($dakoii_current_district)) {
            $district_info = '<div class="dakoii-content-district-info">';
            $district_info .= '<p><strong>' . __('From:', DAKOII_PDM_TEXT_DOMAIN) . '</strong> ';
            $district_info .= '<a href="' . esc_url(home_url('/district/' . $dakoii_current_district->slug)) . '">';
            $district_info .= esc_html($dakoii_current_district->name);
            $district_info .= '</a></p>';
            $district_info .= '</div>';
            
            return $district_info . $content;
        }
        
        return $content;
    }

    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets()
    {
        $district_slug = get_query_var('district_slug');
        
        if (!empty($district_slug)) {
            wp_enqueue_style('dakoii-pdm-frontend');
            wp_enqueue_script('dakoii-pdm-frontend');
        }
    }

    /**
     * AJAX search handler
     */
    public function ajax_search_content()
    {
        if (!wp_verify_nonce($_POST['nonce'], 'dakoii_pdm_nonce')) {
            wp_die('Invalid nonce');
        }

        $query = sanitize_text_field($_POST['query']);
        $district_slug = sanitize_text_field($_POST['district_slug']);

        // Get district and search content
        $district_manager = new Dakoii_PDM_District_Manager();
        $district = $district_manager->read_by_slug($district_slug);

        if (!$district) {
            wp_send_json_error('District not found');
        }

        // Simple search implementation
        $content_manager = new Dakoii_PDM_Content_Manager();
        $content = $content_manager->get_district_content($district->id);

        $results = array();
        foreach ($content as $item) {
            $post = get_post($item->post_id);
            if (stripos($post->post_title, $query) !== false) {
                $results[] = array(
                    'title' => $post->post_title,
                    'url' => get_permalink($post),
                    'excerpt' => wp_trim_words($post->post_content, 20)
                );
            }
        }

        $html = '';
        if (!empty($results)) {
            foreach ($results as $result) {
                $html .= '<div class="search-result">';
                $html .= '<h4><a href="' . esc_url($result['url']) . '">' . esc_html($result['title']) . '</a></h4>';
                $html .= '<p>' . esc_html($result['excerpt']) . '</p>';
                $html .= '</div>';
            }
        } else {
            $html = '<p>' . __('No results found.', DAKOII_PDM_TEXT_DOMAIN) . '</p>';
        }

        wp_send_json_success(array('html' => $html));
    }
}