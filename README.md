# Dakoii Province and District Manager - Installation Guide

## Plugin Installation

1. **Upload Plugin Files**
   - Upload the entire `dakoii-prov-district-manager` folder to your WordPress `wp-content/plugins/` directory
   - Or zip the folder and upload through WordPress admin > Plugins > Add New > Upload Plugin

2. **Activate Plugin**
   - Go to WordPress admin > Plugins
   - Find "Dakoii Province and District Manager" and click "Activate"

3. **Verify Installation**
   - After activation, you should see "Dakoii Manager" in your WordPress admin menu
   - The plugin will automatically create the required database tables

## Initial Setup

### 1. Access Admin Interface
- Navigate to **Dakoii Manager** in your WordPress admin menu
- You'll see the dashboard with statistics and quick actions

### 2. Add Your First Province
- Click **Provinces** > **Add New** or use the "Add New Province" button on dashboard
- Fill in the province details:
  - **Name**: Enter province name (required)
  - **Slug**: Auto-generated or customize (URL-friendly)
  - **Description**: Optional description
  - **Status**: Set to Active

### 3. Add Districts to Province
- Click **Districts** > **Add New**
- Select the province from dropdown
- Fill in district details:
  - **Name**: Enter district name (required)
  - **Slug**: Auto-generated or customize
  - **Description**: Optional description
  - **Logo URL**: Optional district logo
  - **Contact Information**: Email, phone, address, website
  - **Status**: Set to Active

### 4. Associate Content with Districts
- Edit any existing post or page
- In the "District Assignment" meta box (sidebar), select which districts should show this content
- The content will automatically appear in the selected district's mini-website

## Features Overview

### Admin Features
- **Dashboard**: Overview of provinces, districts, and content statistics
- **Province Management**: Full CRUD operations for provinces
- **District Management**: Full CRUD operations for districts with contact info and branding
- **Content Association**: Link posts/pages to specific districts

### Frontend Features
- **District Mini-Websites**: Each district functions as an independent website
- **Dynamic Menus**: Automatic submenu generation for district content
- **Clean URLs**: SEO-friendly district URLs (`/district/district-name/`)
- **Responsive Design**: Mobile-friendly district pages

### URL Structure
- District Home: `/district/{district-slug}/`
- District Posts: `/district/{district-slug}/posts/`
- District Pages: `/district/{district-slug}/pages/`
- Specific Post: `/district/{district-slug}/post/{post-slug}/`
- Specific Page: `/district/{district-slug}/page/{page-slug}/`

## Testing the Plugin

### 1. Test Database Creation
After activation, check that these tables were created in your database:
- `wp_dakoii_provinces`
- `wp_dakoii_districts` 
- `wp_dakoii_district_content`

### 2. Test Admin Interface
- Add a test province
- Add a test district under that province
- Verify the dashboard shows correct statistics

### 3. Test Content Association
- Create or edit a post/page
- Assign it to a district using the meta box
- Verify it appears in the district's content list

### 4. Test Frontend Display
- Visit `/district/{your-district-slug}/` to see the district homepage
- Check that menus and content display correctly

## Troubleshooting

### Common Issues

**Plugin Activation Error**
- Ensure PHP version is 7.4 or higher
- Check that WordPress version is 5.0 or higher
- Verify write permissions for database table creation

**Menu Not Showing**
- Check that your theme supports WordPress menus
- Ensure districts have associated content
- Clear any caching plugins

**District Pages Not Loading**
- Go to Settings > Permalinks and click "Save Changes" to flush rewrite rules
- Verify mod_rewrite is enabled on your server

### Debug Mode
To enable debug mode, add this to your wp-config.php:
```php
define('WP_DEBUG', true);
define('WP_DEBUG_LOG', true);
```

## Plugin Structure

```
dakoii-prov-district-manager/
├── dakoii-prov-district-manager.php (Main plugin file)
├── includes/
│   ├── class-database-manager.php
│   ├── class-province-manager.php
│   ├── class-district-manager.php
│   ├── class-content-manager.php
│   ├── class-menu-manager.php
│   ├── class-admin-interface.php
│   └── class-frontend-handler.php
├── admin/
│   ├── css/admin-styles.css
│   ├── js/admin-scripts.js
│   └── views/
│       ├── dashboard.php
│       ├── provinces-list.php
│       ├── provinces-form.php
│       ├── districts-list.php
│       └── districts-form.php
├── frontend/
│   ├── css/frontend-styles.css
│   └── js/frontend-scripts.js
└── assets/images/
```

## Support

For support and questions:
- **Website**: https://www.dakoiims.com
- **Email**: <EMAIL>

## License

This plugin is licensed under GPL v2 or later.